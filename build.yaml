version: 0.2
env:
  git-credential-helper: yes
phases:
  install: # Install AWS cli, kubectl (needed for Helm) and Helm
    commands:
      - .ci/scripts/config-env.sh
  build: # Build and push Docker image and deploy to EKS
    commands:
      - .ci/scripts/build.sh
  post_build:
    commands:
      # Example tag: 2021.***********
      - git tag -a  $(date +%Y).$(date +%m).$(date +%d).$(date +%H).$(date +%M) -m 'auto-generated post build tag'
      - git push origin --tags
