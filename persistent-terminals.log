[2025-07-15T21:20:09.312Z] Extension deactivated
[2025-07-17T15:35:43.272Z] Extension activated
[2025-07-17T15:35:43.275Z] Created terminal: rspec
[2025-07-17T15:35:43.275Z] Executed command in rspec: dcrc bash
[2025-07-17T15:35:43.275Z] Executed command in rspec: bin/rspec
[2025-07-17T15:35:43.277Z] Created terminal: dev-bash
[2025-07-17T15:35:43.277Z] Executed command in dev-bash: dcrc bundle install
[2025-07-17T15:35:43.277Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-17T15:35:43.277Z] Found existing terminal with given terminal name: dev-bash, Skipping creation..
[2025-07-17T15:35:43.278Z] Executed command in dev-bash: source bin/setenv development
[2025-07-17T15:35:43.278Z] Executed command in dev-bash: dcrc bin/rails console
[2025-07-17T15:35:43.280Z] Created terminal: alu-dev-env
[2025-07-17T15:35:43.280Z] Executed command in alu-dev-env: source bin/setenv development
[2025-07-17T15:35:43.280Z] Executed command in alu-dev-env: dcu alp
[2025-07-17T15:35:43.281Z] Created terminal: sandbox-rails console
[2025-07-17T15:35:43.281Z] Executed command in sandbox-rails console: source bin/setenv sandbox
[2025-07-17T15:35:43.281Z] Executed command in sandbox-rails console: dcrc bin/rails console
[2025-07-17T15:35:43.281Z] Persistent terminals creation completed
