# These environment variables are needed to run the app locally.
# Copy these into a file named .env.development to have them loaded automatically.
AURORA_HOST=localhost
AURORA_PORT=5432
DATABASE_USERNAME=communications_service
DATABASE_PASSWORD=password

AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=
FLIPPER_UI_SECRET=

SOLUTIONS_BY_TEXT_CLIENT_ID=sbt-test-client-id
SOLUTIONS_BY_TEXT_CLIENT_SECRET=sbt-test-client-secret
SOLUTIONS_BY_TEXT_BASE_URL=https://t2c-api-test.solutionsbytext.com
SOLUTIONS_BY_TEXT_AUTH_URL=https://login-test.solutionsbytext.com
SOLUTIONS_BY_TEXT_GROUP_ID=sbt-test-group-id

IDENTITY_SERVICE_AUTH_HEADER=X-Identity-Service-Authorized-Test
