# frozen_string_literal: true

require 'rails_helper'

RSpec.configure do |config|
  # Specify a root folder where Swagger JSON files are generated
  # NOTE: If you're using the rswag-api to serve API descriptions, you'll need
  # to ensure that it's configured to serve Swagger from the same folder
  config.openapi_root = Rails.root.join('swagger').to_s

  # Define one or more Swagger documents and provide global metadata for each one
  # When you run the 'rswag:specs:swaggerize' rake task, the complete Swagger will
  # be generated at the provided relative path under swagger_root
  # By default, the operations defined in spec files are added to the first
  # document below. You can override this behavior by adding a swagger_doc tag to the
  # the root example_group in your specs, e.g. describe '...', swagger_doc: 'v2/swagger.json'
  config.openapi_specs = {
    'v1/swagger.yaml' => {
      openapi: '3.0.1',
      info: {
        title: 'API V1',
        version: 'v1'
      },
      paths: {},
      components: {
        schemas: {
          Message: {
            type: 'object',
            properties: {
              template_key: { type: :string, description: 'internal key used to identify the template associated with a message' },
              recipient: { type: :string, description: 'identifies the recipient of a message (e.g. phone number or email)' },
              delivery_method: { type: :string, enum: %w[SMS EMAIL], description: 'identifies how the message should be delivered (e.g. SMS or email)' },
              inputs: { type: :object, description: 'the inputs used to populate the message template' },
              attribution: { type: :array, items: {
                type: :object,
                description: 'a list of entities to attribute to the message',
                properties: {
                  id: { type: :string, description: 'the id of the corresponding entity' },
                  type: { type: :string, enum: %w[APPLICATION BORROWER DECISION LOAN LOAN_INQUIRY SOURCE] }
                }
              } },
              created_at: { type: :string, format: 'date-time', description: 'timestamp of the initial creation of this record' },
              updated_at: { type: :string, format: 'date-time', description: 'timestamp of the most recent change made to this record' }
            },
            required: %w[template_key recipient delivery_method]
          }
        },
        securitySchemes: {
          token: {
            type: :http,
            scheme: :token
          }
        }
      },
      servers: [
        {
          url: Rails.application.config_for(:swagger).url,
          variables: {
            defaultHost: {
              default: Rails.application.config_for(:swagger).default_host
            }
          }
        }
      ]
    }
  }

  # Specify the format of the output Swagger file when running 'rswag:specs:swaggerize'.
  # The swagger_docs configuration option has the filename including format in
  # the key, this may want to be changed to avoid putting yaml in json files.
  # Defaults to json. Accepts ':json' and ':yaml'.
  config.openapi_format = :yaml
end
