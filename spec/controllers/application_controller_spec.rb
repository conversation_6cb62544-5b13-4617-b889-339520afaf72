# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ApplicationController, type: :controller do
  controller(ApplicationController) do
    def test_action_with_parse_error
      raise ActionDispatch::Http::Parameters::ParseError, 'parse error'
    end
  end

  before do
    routes.draw do
      get 'test_action_with_parse_error' => 'anonymous#test_action_with_parse_error'
    end
  end

  describe 'rescuing from ActionDispatch::Http::Parameters::ParseError' do
    it 'renders a 400 status with error message' do
      get :test_action_with_parse_error

      expect(response.status).to eq(400)
      expect(JSON.parse(response.body)).to eq({ 'errors' => ['parse error'] })
    end
  end
end
