# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TextMessage, type: :model do
  describe 'relationships' do
    it { should have_one(:message) }
    it { should have_many(:text_message_events) }
  end

  describe 'validations' do
    it 'creates a valid text message' do
      expect do
        described_class.create!(vendor_message_id: 'sbt_12345')
      end.to change(described_class, :count).by(1)
    end
  end
end
