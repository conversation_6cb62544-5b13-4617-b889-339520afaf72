# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Message, type: :model do
  let(:template_version) { create(:template_version) }
  let(:template) { create(:template, template_versions: [template_version]) }

  it_behaves_like 'an optimistic locking model' do
    let(:record) { build(:message, :sms) }
  end

  describe 'relationships' do
    it { should belong_to(:template) }
    it { should belong_to(:template_version) }
  end

  describe 'validations' do
    it 'creates a valid message' do
      expect do
        described_class.create!(
          template:,
          template_version:,
          detail_info: {
            recipient: '1-888-888-8888',
            delivery_method: 'SMS'
          }
        )
      end.to change(described_class, :count).by(1)
    end

    it 'does not create a message when missing required detail info attributes' do
      expect do
        described_class.create!(
          template:,
          template_version:,
          detail_info: {
            entity_id: '123456',
            entity_type: 'BORROWER',
            inputs: {
              first_name: 'Test',
              payment_amount: 150.45
            }
          }
        )
      end.to raise_error(ActiveRecord::RecordInvalid)
    end

    it 'only validates the recipient as a phone number when the delivery method is SMS' do
      expect do
        described_class.create!(
          template:,
          template_version:,
          detail_info: {
            recipient: '<EMAIL>',
            delivery_method: 'EMAIL'
          }
        )
      end.to change(described_class, :count).by(1)
    end

    context 'validating template version' do
      let(:message) { create(:message, detail_info: build(:message_detail_info, :email, inputs: { first_name: 'Jorm', last_name: 'Blormbus' })) }

      it 'is valid when message includes all required variables' do
        template_version = create(:template_version, detail_info: build(:template_version_detail_info, variables: %w[first_name last_name]))

        message.update!(template_version:)

        expect(message.valid?).to eq(true)
        expect(message.errors).to be_empty
      end

      it 'is valid when message includes extra variables' do
        template_version = create(:template_version, detail_info: build(:template_version_detail_info, variables: ['first_name']))

        message.update!(template_version:)

        expect(message.valid?).to eq(true)
        expect(message.errors).to be_empty
      end

      it 'is invalid and sets errors when missing variables' do
        template_version = create(:template_version, detail_info: build(:template_version_detail_info, variables: %w[first_name email payment_amount]))

        message.update(template_version:)

        expect(message.valid?).to eq(false)
        expect(message.errors.full_messages).to match_array(['Inputs - email may not be blank', 'Inputs - payment amount may not be blank'])
      end

      it 'is invalid and sets errors when variables are not correctly formatted' do
        variables = %w[
          auto_payment_type
          email
          first_payment_on
          loan_term
          next_scheduled_payment_amount
          past_payment_dates_list
          phone_number
          display_oh_discrimination_disclosure
          loan_agreement_file
        ]

        template_version = create(:template_version, detail_info: build(:template_version_detail_info, variables:))

        inputs = {
          'auto_payment_type' => 1, # should be string
          'email' => '', # should be filled
          'first_payment_on' => '10/11/2022', # should be a 8601 date
          'loan_term' => '10', # should be a number
          'next_scheduled_payment_amount' => '200.00', # should be a numeric money amount
          'payment_amount' => -100.00, # should not be negative
          'past_payment_dates_list' => '2023-05-27, 2023-06-29, 2023-07-28', # should be an array
          'phone_number' => '5551111', # should be full phone
          'display_oh_discrimination_disclosure' => 'yes', # should be a boolean
          'loan_agreement_file' => { 'download_url' => 'file.pdf' } # should be a file
        }

        invalid_message = build(:message, template_version:, detail_info: build(:message_detail_info, :email, inputs:))

        expect(invalid_message.valid?).to eq(false)

        errors = [
          'Inputs - auto payment type must be a string',
          'Inputs - email may not be blank',
          'Inputs - first payment on must be a date in iso8601 format %Y-%m-%d',
          'Inputs - loan term must be a number',
          'Inputs - next scheduled payment amount must be a numeric money amount',
          'Inputs - past payment dates list must be an array',
          'Inputs - phone number must be a valid phone number',
          'Inputs - display oh discrimination disclosure must be a boolean value or a boolean string',
          'Inputs - loan agreement file must be a Hash containing `filename`, `download_url`, and `type` keys'
        ]

        expect(invalid_message.errors.full_messages).to match_array(errors)
      end
    end
  end

  describe '#detail_info' do
    it 'stores nested objects as json' do
      message = create(
        :message,
        detail_info: build(:message_detail_info, :sms, entities: build_list(:message_entity, 3))
      )

      entities_type = Message.select("jsonb_typeof(detail_info->'entities') as typeof").where(id: message.id).first
      expect(entities_type.typeof).to eq('array')
      entity_type = Message.select("jsonb_typeof(detail_info->'entities'->0) as typeof").where(id: message.id).first
      expect(entity_type.typeof).to eq('object')
    end

    it 'raises an error if an invalid entity type is provided' do
      expect { create(:message, detail_info: build(:message_detail_info, :email, entities: [build(:message_entity, type: 'UNKNOWN')])) }.to raise_error(ArgumentError, /invalid value 'UNKNOWN' is assigned/)
    end

    %w[APPLICATION BORROWER DECISION LOAN LOAN_INQUIRY SOURCE].each do |entity_type|
      it "permits the '#{entity_type}' entity type to be populated" do
        entity = build(:message_entity, type: entity_type)
        message = create(:message, detail_info: build(:message_detail_info, :email, entities: [entity]))
        expect(message).to be_valid
        expect(message.detail_info.entities.first).to eq(entity)
      end
    end
  end

  describe '#associated_loan_id' do
    it 'returns the loan_id within entities' do
      message = create(
        :message,
        detail_info: build(:message_detail_info, :sms, entities: build_list(:message_entity, 1) { |ent| ent[:type] = 'LOAN' })
      )

      expect(message.associated_loan_id).to eq(message.detail_info.entities.first.id)
    end
  end
end
