# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TextMessageEvent, type: :model do
  let(:valid_attributes) do
    {
      text_message: build(:text_message),
      status_code: 1,
      payload: {
        accountId: 'string',
        message: 'string'
      }
    }
  end

  describe 'relationships' do
    it { should belong_to(:text_message) }
  end

  describe 'validations' do
    it 'creates a valid text message event' do
      expect do
        described_class.create!(valid_attributes)
      end.to change(described_class, :count).by(1)
    end

    it { should validate_numericality_of(:status_code).only_integer }
  end
end
