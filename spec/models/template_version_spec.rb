# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TemplateVersion, type: :model do
  let(:attributes) do
    attributes_for(:template_version).merge(template: create(:template))
  end

  describe 'relationships' do
    it { should belong_to(:template) }
    it { should have_many(:messages) }
  end

  describe 'validations' do
    it 'creates a valid template version' do
      expect do
        described_class.create!(attributes)
      end.to change(described_class, :count).by(1)
    end

    it 'creates a template version with supported variables' do
      attributes[:detail_info] = TemplateVersion::DetailInfo.new(status: 'ACTIVE', variables: TemplateVariables::VARIABLES.keys.map(&:to_s))
      expect do
        described_class.create!(attributes)
      end.to change(described_class, :count).by(1)
    end

    it 'errors when given unsupported variables' do
      attributes[:detail_info] = TemplateVersion::DetailInfo.new(status: 'ACTIVE', variables: %w[first_name something else])
      invalid_instance = described_class.new(attributes)

      expect(invalid_instance).not_to be_valid
      expect(invalid_instance.errors.full_messages).to include(/Detail info is invalid/)
      expect(invalid_instance.detail_info.errors.full_messages).to include(/Variables The following are unsupported: \["something", "else"\]/)
    end

    it 'does not create a template version when missing a status' do
      attributes[:detail_info].status = nil

      expect do
        described_class.create!(attributes)
      end.to raise_error(ActiveRecord::RecordInvalid)
    end
  end
end
