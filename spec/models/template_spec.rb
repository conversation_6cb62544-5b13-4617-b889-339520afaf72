# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Template, type: :model do
  let(:attributes) do
    attributes_for(:template)
  end

  it_behaves_like 'an optimistic locking model' do
    let(:record) { build(:template) }
  end

  describe 'relationships' do
    it { should have_many(:messages) }
    it { should have_many(:template_versions) }
  end

  describe 'validations' do
    it 'creates a valid template' do
      expect do
        described_class.create!(attributes)
      end.to change(described_class, :count).by(1)
    end

    it 'does not create a template when missing a key' do
      attributes[:detail_info].key = nil

      expect do
        described_class.create!(attributes)
      end.to raise_error(ActiveRecord::RecordInvalid)
    end

    it 'does not create a template when missing a name' do
      attributes[:detail_info].name = nil

      expect do
        described_class.create!(attributes)
      end.to raise_error(ActiveRecord::RecordInvalid)
    end

    it 'does not create a template when missing a topic' do
      attributes[:detail_info].topic = nil

      expect do
        described_class.create!(attributes)
      end.to raise_error(ActiveRecord::RecordInvalid)
    end
  end

  describe '#active_version' do
    it 'returns the active template version' do
      template = create(:template)
      active_template_version = create(:template_version, template:, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::ACTIVE_STATUS))
      create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::ARCHIVED_STATUS))
      create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::DRAFT_STATUS))
      create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::PAUSED_STATUS))

      other_template = create(:template)
      create(:template_version, template: other_template, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::ACTIVE_STATUS))

      expect(template.active_version).to eq(active_template_version)
    end

    it 'returns nil when no active version' do
      template = create(:template)
      create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::ARCHIVED_STATUS))
      create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::DRAFT_STATUS))
      create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::PAUSED_STATUS))

      other_template = create(:template)
      create(:template_version, template: other_template, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::ACTIVE_STATUS))

      expect(template.active_version).to be_nil
    end
  end
end
