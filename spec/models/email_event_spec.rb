# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailEvent, type: :model do
  let(:valid_attributes) do
    {
      email: build(:email),
      event: 'processed',
      payload: {
        email: '<EMAIL>',
        timestamp: 1_513_299_569,
        'smtp-id': '<14c5d75ce93.dfd.64b469@ismtpd-555>',
        event: 'processed',
        category: 'cat facts',
        sg_event_id: 'sg_event_id',
        sg_message_id: 'sg_message_id'
      }
    }
  end

  describe 'relationships' do
    it { should belong_to(:email) }
  end

  describe 'validations' do
    it 'creates a valid email event' do
      expect do
        described_class.create!(valid_attributes)
      end.to change(described_class, :count).by(1)
    end

    it { should validate_presence_of(:event) }
    it { should validate_presence_of(:payload) }
  end
end
