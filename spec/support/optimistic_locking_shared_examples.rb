# frozen_string_literal: true

RSpec.shared_examples 'an optimistic locking model' do
  it 'raises an exception when a save would overwrite data' do
    record.save!
    other_record = record.class.find(record.id)

    expect do
      record.update!(updated_at: Time.zone.now)
      other_record.update!(updated_at: Time.zone.now)
    end.to raise_error(ActiveRecord::StaleObjectError)

    expect(other_record.reload.update!(updated_at: Time.zone.now)).to eq(true)
  end
end
