# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailCampaign::UploadContacts, type: :service do
  let(:contacts_file_path) { Rails.root.join('spec', 'support', 'files', 'email_campaign_mock.csv') }
  let(:contacts_file) { Rack::Test::UploadedFile.new(contacts_file_path, 'text/csv') }
  let(:campaign_name) { 'Test Campaign' }

  subject { described_class.new(contacts_file:, campaign_name:) }

  describe 'validations' do
    context 'when contacts_file is valid' do
      it 'is permitted' do
        expect(subject).to be_valid
      end
    end

    context 'when contacts_file is not a CSV' do
      let(:contacts_file) { Rack::Test::UploadedFile.new(contacts_file_path, 'text/pdf') }

      it 'is not valid' do
        expect(subject).not_to be_valid
        expect(subject.errors[:contacts_file]).to include('must be a CSV file')
      end
    end

    context 'when contacts_file has no EMAIL column' do
      let(:contacts_file_path) { Rails.root.join('spec', 'support', 'files', 'email_campaign_mock-missing_email.csv') }

      it 'is not valid' do
        expect(subject).not_to be_valid
        expect(subject.errors[:contacts_file]).to include("must include an 'EMAIL' column")
      end
    end

    context 'when contacts_file has no LOAN_ID column' do
      let(:contacts_file_path) { Rails.root.join('spec', 'support', 'files', 'email_campaign_mock-missing_loan_id.csv') }

      it 'is not valid' do
        expect(subject).not_to be_valid
        expect(subject.errors[:contacts_file]).to include("must include a 'LOAN_ID' column")
      end
    end
  end

  describe '#call' do
    let(:s3_client) { Aws::S3::Client.new(stub_responses: { put_object: {} }) }

    before do
      allow(s3_client).to receive(:put_object)
      allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
    end

    it 'uploads the contact file to s3' do
      subject.call
      expect(s3_client).to have_received(:put_object)
        .with(bucket: 'above-lending-communication-service-sandbox',
              key: "email_campaigns/#{campaign_name}.csv",
              body: contacts_file)
    end
  end
end
