# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Auth::VerifyOauthToken, type: :service do
  let(:subject) { described_class.new(authorization_header:) }
  let(:authorization_header) { 'Bearer oauth-token-abc123' }
  let(:token_type) { 'oauth2' }

  before { allow(Auth::DecodeJwt).to receive(:call).with(token: 'oauth-token-abc123').and_return(Auth::DecodedToken.new(type: token_type, data: {})) }

  describe '#call' do
    it 'returns true when the provided token is authorized' do
      expect(subject.call).to eq(true)
    end

    context 'when no authorization header is provided' do
      let(:authorization_header) { nil }

      it 'raises an error' do
        expect { subject.call }.to raise_error(ActiveModel::ValidationError, /Authorization header can't be blank/i)
      end
    end

    context 'when a blank authorization header is provided' do
      let(:authorization_header) { '' }

      it 'raises an error' do
        expect { subject.call }.to raise_error(ActiveModel::ValidationError, /Authorization header can't be blank/i)
      end
    end

    context 'when the authorization header does not contain a bearer token' do
      let(:authorization_header) { 'api-key-abc123' }

      it 'raises an error' do
        expect { subject.call }.to raise_error(Auth::AuthenticationError, /Missing Bearer token/i)
      end
    end

    context 'when the provided bearer token is not an OAuth token' do
      let(:token_type) { 'custom' }

      it 'raises an error' do
        expect { subject.call }.to raise_error(Auth::AuthorizationError, /Invalid token type/i)
      end
    end

    context 'when an error occurs decoding the token' do
      let(:test_error) { Auth::AuthorizationError.new('Invalid JWT token') }

      before { allow(Auth::DecodeJwt).to receive(:call).and_raise(test_error) }

      it 'raises an error' do
        expect { subject.call }.to raise_error(test_error)
      end
    end
  end
end
