# frozen_string_literal: true

# spec/requests/api/messages_spec.rb
require 'swagger_helper'
require 'template_seed_data'

def message_example(template_key:, inputs:, delivery_method:)
  request_body_example(
    value: {
      template_key:,
      recipient: delivery_method == 'SMS' ? '15556663322' : '<EMAIL>',
      delivery_method:,
      inputs:,
      attribution: [
        {
          id: '43059c15-a4e3-46fd-a703-ea65ff057344',
          type: 'LOAN'
        },
        {
          id: '86f3f5d1-ee31-4b35-895c-35ebd4fc98f8',
          type: 'BORROWER'
        }
      ]
    },
    name: template_key, summary: "#{template_key.titleize} #{delivery_method}"
  )
end

describe 'Messages API' do
  let(:blank_template_version) do
    create(
      :template_version,
      detail_info: build(:template_version_detail_info, status: 'ACTIVE', variables: [])
    )
  end

  before do
    TemplateSeedData.seed!
    create(:template, template_versions: [blank_template_version])
  end

  path '/api/messages' do
    post 'Creates a message' do
      tags 'Messages'
      consumes 'application/json'
      description 'Example request body for creating a new Message in the Communications Service'
      parameter(
        name: :root,
        in: :body,
        schema: {
          type: :object,
          properties: {
            template_key: { type: :string, description: 'internal key used to identify the template associated with a message' },
            recipient: { type: :string, description: 'identifies the recipient of a message (e.g. phone number or email)' },
            delivery_method: { type: :string, enum: %w[SMS EMAIL], description: 'identifies how the message should be delivered (e.g. SMS or email)' },
            inputs: { type: :object, properties: {} },
            attribution: {
              type: :array, items: {
                type: :object, properties: {
                  id: { type: :string },
                  type: { type: :string, enum: %w[APPLICATION BORROWER DECISION LOAN LOAN_INQUIRY SOURCE] }
                }
              }
            }
          }
        }
      )

      TemplateSeedData.default_text_message_templates.each do |template_data|
        inputs = template_data[:variables].to_h do |var|
          template_variable = TemplateVariables::VARIABLES[var.to_sym]

          [var.to_sym, template_variable.default_value]
        end

        message_example(template_key: template_data[:key], inputs:, delivery_method: 'SMS')
      end

      TemplateSeedData.default_email_templates.each do |template_data|
        inputs = template_data[:variables].to_h do |var|
          template_variable = TemplateVariables::VARIABLES[var.to_sym]

          [var.to_sym, template_variable.default_value]
        end

        message_example(template_key: template_data[:key], inputs:, delivery_method: 'EMAIL')
      end

      response '201', 'message created' do
        let(:root) do
          {
            template_key: 'payment_past_due_10_days',
            recipient: '15556665555',
            delivery_method: 'SMS',
            inputs: {},
            attribution: [
              {
                id: '43059c15-a4e3-46fd-a703-ea65ff057344',
                type: 'LOAN'
              },
              {
                id: '86f3f5d1-ee31-4b35-895c-35ebd4fc98f8',
                type: 'BORROWER'
              }
            ]
          }
        end

        schema '$ref' => '#/components/schemas/Message'
        run_test!
      end
    end

    get 'Retrieve messages' do
      tags 'Messages'
      produces 'application/json'
      description 'Returns a paginated list of messages. Each message can be filtered by various parameters.'
      MessageFilter::ATTRIBUTION_FILTERS.each do |filter|
        parameter(
          name: "filter[#{filter}]",
          in: :query,
          description: "Filters messages whose attributed entity matches the specified #{filter} attribute.",
          required: false,
          schema: { type: 'string' }
        )
      end
      MessageFilter::SINGULAR_DETAIL_INFO_FILTERS.each do |filter|
        parameter(
          name: "filter[#{filter}]",
          in: :query,
          description: "Filters messages to those with the specified #{filter} attribute.",
          required: false,
          schema: { type: 'string' }
        )
      end
      MessageFilter::PLURAL_DETAIL_INFO_FILTERS.each do |filter|
        parameter(
          name: "filter[#{filter}]",
          in: :query,
          description: "Filters messages to those with the #{filter.singularize} attribute is any one of the specified values.",
          required: false,
          schema: { type: 'string' }
        )
      end
      MessageFilter::NEGATION_DETAIL_INFO_FILTERS.each do |filter|
        parameter(
          name: "filter[#{filter}]",
          in: :query,
          description: "Filters messages to those with the #{filter[4..]} attribute is NOT the specified value.",
          required: false,
          schema: { type: 'string' }
        )
      end
      parameter(
        name: "filter[#{MessageFilter::TEMPLATE_KEY_FILTER}]",
        in: :query,
        description: 'Filters messages to those triggered with the specified template.',
        required: false,
        schema: { type: 'string' }
      )
      parameter(
        name: "filter[#{MessageFilter::PLURAL_TEMPLATE_KEY_FILTER}]",
        in: :query,
        description: 'Filters messages to those triggered with any one of the specified templates.',
        required: false,
        schema: { type: 'string' }
      )
      MessageFilter::TIMESTAMP_FILTERS.each do |timestamp_filter|
        parameter(
          name: "filter[#{timestamp_filter}]",
          in: :query,
          description: "Filters messages to those whose #{timestamp_filter.split('_').first} at timestamp is #{timestamp_filter.split('_').last} the specified date/time.",
          required: false,
          schema: { type: 'string' }
        )
      end
      parameter(
        name: :per_page,
        in: :query,
        description: 'Determines the number of messages per page.',
        required: false,
        schema: { type: 'integer', default: 10 }
      )
      parameter(
        name: :page,
        in: :query,
        description: 'Specifies the page number.',
        required: false,
        schema: { type: 'integer', default: 1 }
      )

      response '200', 'messages found' do
        schema(
          type: :object,
          properties: {
            messages: { type: 'array', items: { '$ref' => '#/components/schemas/Message' } },
            meta: {
              type: :object,
              properties: {
                count: { type: :number },
                total_count: { type: :number },
                per_page: { type: :number },
                current_page: { type: :number },
                total_pages: { type: :number }
              }
            }
          }
        )
        run_test!
      end
    end
  end

  path '/api/messages/{id}' do
    get 'Retrieves a message' do
      tags 'Messages'
      produces 'application/json'
      parameter name: :id, in: :path, type: :string

      response '200', 'message found' do
        let(:id) { create(:message, :email, template: blank_template_version.template, template_version: blank_template_version).id }

        schema '$ref' => '#/components/schemas/Message'
        run_test!
      end
    end
  end
end
