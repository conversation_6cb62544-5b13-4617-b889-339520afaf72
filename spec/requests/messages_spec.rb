# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Messages', type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let(:template_version) { create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::ACTIVE_STATUS)) }
  let(:valid_template) { create(:template, template_versions: [template_version], detail_info: build(:template_detail_info, key: 'payment_past_due_5_days')) }

  let(:limited_template_version) { create(:template_version, detail_info: build(:template_version_detail_info, status: TemplateVersion::DetailInfo::ACTIVE_STATUS)) }
  let(:limited_valid_template) { create(:template, template_versions: [limited_template_version], detail_info: build(:template_detail_info, key: 'offer_expiration_retargeting_fomo', limit_per_borrower: 1)) }

  let!(:message1) { create(:message, created_at: 5.days.ago, detail_info: build(:message_detail_info, :sms)) }
  let!(:message2) { create(:message, created_at: 4.days.ago, detail_info: build(:message_detail_info, :sms)) }
  let!(:message3) { create(:message, created_at: 3.days.ago, detail_info: build(:message_detail_info, :sms)) }
  let!(:message4) { create(:message, created_at: 2.days.ago, detail_info: build(:message_detail_info, :email)) }

  let(:message_params) do
    {
      template_key: valid_template.detail_info.key,
      recipient: '13121234567',
      delivery_method: 'SMS',
      inputs: {
        first_name: 'John',
        payment_amount: 123.45
      },
      attribution: [
        {
          id: SecureRandom.uuid,
          type: 'LOAN'
        },
        {
          id: SecureRandom.uuid,
          type: 'BORROWER'
        }
      ]
    }
  end

  let(:limited_message_params) do
    message_params.merge(template_key: limited_valid_template.detail_info.key)
  end

  describe 'GET /api/messages' do
    it 'returns all messages when no filters are provided' do
      message_filters = {}
      get api_messages_path, params: message_filters

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['messages'].size).to eq(4)
    end

    it 'returns filtered messages' do
      message_filters = { filter: { delivery_method: 'SMS' } }
      get api_messages_path, params: message_filters

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['messages'].size).to eq(3)
    end

    it 'returns an error when unsupported filters are used' do
      message_filters = { filter: { foo: 'bar' } }
      get api_messages_path, params: message_filters

      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)).to have_key('errors')
    end

    it 'returns a set of paginated messages' do
      get api_messages_path, params: { page: 1, per_page: 2 }
      first_page = JSON.parse(response.body)
      expect(first_page['messages'].map { |m| m['id'] }).to contain_exactly(message1.id, message2.id)
      expect(first_page['meta']).to eq({ 'count' => 2, 'total_count' => 4, 'per_page' => 2, 'current_page' => 1, 'total_pages' => 2 })

      get api_messages_path, params: { page: 2, per_page: 2 }
      second_page = JSON.parse(response.body)
      expect(second_page['messages'].map { |m| m['id'] }).to contain_exactly(message3.id, message4.id)
      expect(second_page['meta']).to eq({ 'count' => 2, 'total_count' => 4, 'per_page' => 2, 'current_page' => 2, 'total_pages' => 2 })
    end

    it 'uses a default page size of 10' do
      get api_messages_path
      expect(JSON.parse(response.body).dig('meta', 'per_page')).to eq(10)
    end
  end

  describe 'GET /api/messages/:id' do
    it 'returns the message if it exists' do
      get api_message_path(message1.id)

      expect(response).to have_http_status(200)
      expect(JSON.parse(response.body)['id']).to eq(message1.id)
    end

    it 'returns an error when the message does not exist' do
      allow(Rails.logger).to receive(:error)

      invalid_id = 'invalid-id'
      get api_message_path(invalid_id)

      expect(Rails.logger).to have_received(:error).with("Message record not found for the following ID: #{invalid_id}", error_message: "Couldn't find Message with 'id'=#{invalid_id}", message_id: invalid_id)

      expect(response).to have_http_status(404)
      expect(JSON.parse(response.body)['errors']).to eq([{ 'message' => "Couldn't find Message with 'id'=#{invalid_id}" }])
    end
  end

  describe 'POST /api/messages' do
    let(:message_id) { JSON.parse(response.body, symbolize_names: true)[:id] }

    before do
      # Around noon in central time should generally be schedulable.
      travel_to('2022-03-14T18:00:00Z'.to_time)
    end

    context 'with valid parameters' do
      let(:message_id) { JSON.parse(response.body, symbolize_names: true)[:id] }

      it 'Accepts messages up until the configured per-borrower limit' do
        expect do
          post api_messages_path, params: limited_message_params, as: :json
        end.to change(Message, :count).by(1)

        expect(Message.find(message_id)).to be_persisted
        expect(Rails.logger).to receive(:log).with(anything).at_least(:once)

        expect(response).to be_successful

        expect do
          post api_messages_path, params: limited_message_params, as: :json
        end.to change(Message, :count).by(0)

        expect(response.status).to eq(429)

        # NOTE:  Allows other messages to be sent to this borrower with a different template
        expect do
          post api_messages_path, params: message_params, as: :json
        end.to change(Message, :count).by(1)

        expect(Message.find(message_id)).to be_persisted
        expect(response).to be_successful
      end
    end
  end
end
