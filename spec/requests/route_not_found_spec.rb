# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Route not found', type: :request do
  describe 'GET /non_existent_path' do
    it 'returns a 404 status code' do
      get '/non_existent_path'
      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'POST /another_non_existent_path' do
    it 'returns a 404 status code for POST requests' do
      post '/another_non_existent_path'
      expect(response).to have_http_status(:not_found)
    end
  end
end
