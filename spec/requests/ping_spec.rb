# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'ping requests', type: :request do
  describe '#index' do
    let(:git_sha) { 'GIT_SHA' }

    before do
      @original_git_sha = ENV.fetch('GIT_SHA') # Store the original value
      ENV['GIT_SHA'] = git_sha # Set the desired test value
    end

    after do
      ENV['GIT_SHA'] = @original_git_sha # Restore the original value
    end

    it 'responds ok' do
      get '/ping'

      expected_body = {
        result: 'pong',
        git_sha:
      }.stringify_keys

      expect(response).to be_ok
      expect(JSON.parse(response.body)).to eq(expected_body)
    end
  end
end
