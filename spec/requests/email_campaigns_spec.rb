# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'EmailCampaigns', type: :request do
  let(:campaign_name) { 'test_campaign' }

  describe 'PUT /utils/email_campaigns/:campaign_name/contacts_file' do
    let(:contacts_file_path) { Rails.root.join('spec', 'support', 'files', 'email_campaign_mock.csv') }
    let(:contacts_file) { Rack::Test::UploadedFile.new(contacts_file_path, 'text/csv') }

    before do
      s3_client = Aws::S3::Client.new(stub_responses: { put_object: {} })
      allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
    end

    context 'with correct file type' do
      it 'uploads the contacts file successfully' do
        put utils_upload_contacts_file_path(campaign_name), params: { contacts_file: }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to eq("Contacts file uploaded successfully for campaign #{campaign_name}")
      end
    end

    context 'with invalid file type' do
      let(:contacts_file) { Rack::Test::UploadedFile.new(contacts_file_path, 'text/pdf') }

      it 'returns an error for invalid contacts file' do
        put utils_upload_contacts_file_path(campaign_name), params: { contacts_file: }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['errors']).to match_array(['Contacts file must be a CSV file'])
      end
    end

    context 'when an exception occurs' do
      before do
        allow_any_instance_of(EmailCampaign::UploadContacts).to receive(:call).and_raise(StandardError, 'Unexpected error')
      end

      it 'returns an internal server error' do
        put utils_upload_contacts_file_path(campaign_name), params: { contacts_file: }

        expect(response).to have_http_status(:internal_server_error)
        expect(JSON.parse(response.body)['error']).to eq('Unexpected error')
      end
    end
  end

  describe 'POST /utils/:campaign_name/trigger' do
    let(:template_name) { 'test_template' }
    let(:delivery_rate) { 100 }

    it 'enqueues email campaign job successfully' do
      post utils_trigger_campaign_path(campaign_name), params: { campaign_name:, template_name:, delivery_rate: }, as: :json

      expect(EmailCampaignJob::Coordinator).to have_enqueued_sidekiq_job.with(campaign_name, template_name, delivery_rate)

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['message']).to eq(
        "Email campaign job #{campaign_name} with template: #{template_name} successfully enqueued"
      )
      expect(JSON.parse(response.body)['job_id']).to be_present
    end
  end
end
