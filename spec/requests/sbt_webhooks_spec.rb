# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'SbtWebhooks', type: :request do
  let(:message_status_params) do
    {
      Type: 'MessageStatus',
      Payload: {
        AccountId: SecureRandom.uuid,
        Message: 'test message',
        Msisdn: '1-888-888-8888',
        GroupName: 'Account Group',
        GroupId: SecureRandom.uuid,
        CommunicationCode: '12345',
        DeliveredTime: '2023-06-09T19:58:10.6012211Z',
        Properties: nil,
        StatusCode: '100',
        StatusCodeDescription: 'Delivered',
        MessageId: SecureRandom.uuid,
        ReferenceId: '',
        TotalMessageSegments: 2,
        MessageType: 'Unicast'
      }
    }
  end

  describe 'POST /api/sbt_webhooks' do
    shared_examples 'an SBT webhook request' do
      context 'when webhook Type is not MessageStatus' do
        it 'logs an error message' do
          message_status_params[:Type] = 'InvalidMessageStatus'
          expect(Rails.logger).to receive(:error).with("Webook Status Type #{message_status_params[:Type]} Not Supported")
          expect { subject }.not_to enqueue_sidekiq_job

          expect(response).to have_http_status(:accepted)
        end
      end

      context 'when webhook Type is MessageStatus' do
        it 'triggers the SbtOutboundMessageStatusJob and responds with status code 200' do
          arguments = message_status_params[:Payload].values_at(:MessageId, :StatusCode, :DeliveredTime) << message_status_params.to_json

          expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
          expect { subject }.to(enqueue_sidekiq_job(SbtOutboundMessageStatusJob).with(*arguments))
          expect(response).to have_http_status(:accepted)
        end
      end
    end

    context 'when an OAuth token is included in the request headers' do
      let(:headers) { { 'Authorization' => 'Bearer oauth-token' } }
      let(:decoded_token) { instance_double(Auth::DecodedToken, type: 'oauth2', data: {}) }

      subject { post api_sbt_webhooks_path, params: message_status_params, as: :json, headers: }

      before do
        allow(Auth::DecodeJwt).to receive(:call).with(token: 'oauth-token').and_return(decoded_token)
      end

      it_behaves_like 'an SBT webhook request'
    end

    context 'when no authentication or authorization is attached to the request' do
      subject { post api_sbt_webhooks_path, params: message_status_params, as: :json }

      it 'returns an unauthorized response' do
        expect { subject }.not_to enqueue_sidekiq_job

        expect(response).to have_http_status(:unauthorized)
        expect(response.body).to include('Authorization header can\'t be blank')
      end
    end

    context 'when an invalid authentication or authorization header is attached to the request' do
      let(:headers) { { 'Authorization' => 'Bearer test-123' } }

      subject { post api_sbt_webhooks_path, params: message_status_params, as: :json, headers: }

      it 'returns a forbidden response' do
        expect { subject }.not_to enqueue_sidekiq_job

        expect(response).to have_http_status(:forbidden)
        expect(response.body).to include('Invalid JWT token')
      end
    end
  end
end
