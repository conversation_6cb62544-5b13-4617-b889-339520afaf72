# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Messages', type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let(:template_version) do
    create(
      :template_version,
      detail_info: build(
        :template_version_detail_info,
        variables:,
        status: TemplateVersion::DetailInfo::ACTIVE_STATUS
      )
    )
  end
  let(:variables) { %w[first_name payment_amount] }
  let(:detail_info) { build(:template_detail_info, key: 'payment_past_due_5_days') }
  let(:valid_template) { create(:template, template_versions: [template_version], detail_info:) }

  let(:inputs) { { first_name: '<PERSON>', payment_amount: 123.455 } }
  let(:message_params) do
    {
      template_key: valid_template.detail_info.key,
      recipient: '<EMAIL>',
      delivery_method: 'EMAIL',
      inputs:,
      attribution: [
        {
          id: SecureRandom.uuid,
          type: 'LOAN'
        },
        {
          id: SecureRandom.uuid,
          type: 'BORROWER'
        }
      ]
    }
  end

  describe 'POST /api/messages' do
    before do
      # Around noon in central time should generally be schedulable.
      travel_to('2022-03-14T18:00:00Z'.to_time)
    end

    context 'with valid parameters' do
      let(:message_id) { JSON.parse(response.body, symbolize_names: true)[:id] }

      it 'creates a new Message' do
        expect do
          post api_messages_path, params: message_params, as: :json
        end.to change(Message, :count).by(1)

        last_message = Message.find(message_id)
        expect(last_message.template_id).to eq(valid_template.id)
        expect(last_message.detail_info['recipient']).to eq(message_params[:recipient])
        expect(last_message.detail_info['delivery_method']).to eq(message_params[:delivery_method])
        expect(last_message.detail_info['inputs']['first_name']).to eq(message_params[:inputs][:first_name])
        expect(last_message.detail_info['inputs']['payment_amount'].to_d).to eq(message_params[:inputs][:payment_amount])
        expect(last_message.detail_info.entities.first[:id]).to eq(message_params[:attribution].first[:id])
        expect(last_message.detail_info.entities.first[:type]).to eq(message_params[:attribution].first[:type])
      end

      it 'triggers the delivery of a Message' do
        post api_messages_path, params: message_params, as: :json

        expect(Message.where(id: message_id)).to exist
        expect(SendEmailJob).to have_enqueued_sidekiq_job(message_id)
      end

      it 'returns a 201 status code' do
        post api_messages_path, params: message_params, as: :json
        expect(response).to have_http_status(:created)
      end
    end

    context 'with a template with attachments' do
      let(:variables) { %w[first_name loan_agreement_file] }
      let(:detail_info) { build(:template_detail_info, key: 'stamped_loan_agreement') }
      let(:loan_agreement_file) { { filename: 'sample.pdf', download_url: '/sample.pdf', type: 'application/pdf' } }
      let(:inputs) { { first_name: 'John', loan_agreement_file: } }

      let(:message_id) { JSON.parse(response.body, symbolize_names: true)[:id] }

      it 'creates the message' do
        expect do
          post api_messages_path, params: message_params, as: :json
        end.to change(Message, :count).by(1)

        last_message = Message.find(message_id)
        expect(last_message.detail_info['inputs']['loan_agreement_file']).to eq(loan_agreement_file.stringify_keys)
      end
    end

    context 'with invalid parameters' do
      before do
        allow(Rails.logger).to receive(:error)
      end

      it 'does not process a message when given no template key' do
        message_params[:template_key] = ''

        expect do
          expect do
            post api_messages_path, params: message_params, as: :json
          end.to change(Message, :count).by(0)
        end.not_to enqueue_sidekiq_job

        expect(Rails.logger).to have_received(:error).with(/Template must exist/)
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'logs an error when recipient missing' do
        message_params[:recipient] = ''

        post api_messages_path, params: message_params, as: :json

        expect(Rails.logger).to have_received(:error).with(/recipient: blank/)
      end

      it 'logs an error when a template is not valid' do
        message_params[:template_key] = valid_template.detail_info.key
        message_params[:inputs][:first_name] = ''

        post api_messages_path, params: message_params, as: :json

        expect(Rails.logger).to have_received(:error).with(/Invalid message creation attempt:/)
      end

      it 'logs an error when a template has no active version' do
        template_version.detail_info.status = 'ARCHIVED'
        template_version.save!

        post api_messages_path, params: message_params, as: :json

        expect(Rails.logger).to have_received(:error).with(/Template version must exist/)
      end
    end
  end
end
