# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'SendgridWebhooks', type: :request do
  let(:sendgrid_params) do
    {
      _json: [{
        attempt: '1',
        bounce_classification: 'bounced',
        category: 'cat facts',
        email: '<EMAIL>',
        event: 'dropped',
        ip: '*********',
        reason: 'Bounced Address',
        response: '500 unknown recipient',
        sg_event_id: SecureRandom.uuid,
        sg_message_id: SecureRandom.uuid,
        'smtp-id': SecureRandom.uuid,
        status: '5.0.0',
        timestamp: 1_513_299_569,
        message_id: SecureRandom.uuid
      }, {
        attempt: '2',
        bounce_classification: 'bounced',
        category: 'cat facts',
        email: '<EMAIL>',
        event: 'processed',
        ip: '*********',
        reason: 'Bounced Address',
        response: '500 unknown recipient',
        sg_event_id: SecureRandom.uuid,
        sg_message_id: SecureRandom.uuid,
        'smtp-id': SecureRandom.uuid,
        status: '5.0.0',
        timestamp: 1_513_299_569,
        message_id: SecureRandom.uuid
      }]
    }
  end
  let(:headers) { {} }

  describe 'POST /api/sendgrid_webhooks' do
    before do
      allow(DatadogSpanTagger).to receive(:add_event_tags_to_span)
    end

    shared_examples 'a SendGrid webhook request' do
      it 'enqueues a SendgridEmailDeliveryEventJob and returns accepted status' do
        expect do
          post(api_sendgrid_webhooks_path, params: sendgrid_params, as: :json, headers:)
        end.to(enqueue_sidekiq_job(SendgridEmailDeliveryEventJob).with(
          sendgrid_params[:_json].first[:event],
          sendgrid_params[:_json].first[:message_id],
          sendgrid_params[:_json].first[:timestamp],
          sendgrid_params[:_json].first.to_json
        ).and(enqueue_sidekiq_job(SendgridEmailDeliveryEventJob).with(
                sendgrid_params[:_json].second[:event],
                sendgrid_params[:_json].second[:message_id],
                sendgrid_params[:_json].first[:timestamp],
                sendgrid_params[:_json].second.to_json
              )))

        expect(response).to have_http_status(:accepted)
      end

      it 'does not enqueue a SendgridEmailDeliveryEventJob when receiving an unrecognized event' do
        sendgrid_params[:_json].first[:event] = 'group_unsubscribe'
        sendgrid_params[:_json].second[:event] = 'invalid'

        expect do
          post(api_sendgrid_webhooks_path, params: sendgrid_params, as: :json, headers:)
        end.not_to enqueue_sidekiq_job(SendgridEmailDeliveryEventJob)

        expect(response).to have_http_status(:accepted)
      end

      it 'does not enqueue a SendgridEmailDeliveryEventJob when payload is missing a message_id' do
        sendgrid_params[:_json].first[:message_id] = nil
        sendgrid_params[:_json].second[:message_id] = nil

        expect do
          post(api_sendgrid_webhooks_path, params: sendgrid_params, as: :json, headers:)
        end.not_to enqueue_sidekiq_job(SendgridEmailDeliveryEventJob)

        expect(response).to have_http_status(:accepted)
      end

      it 'create an event in datadog when Sendgrid payload includes unrecognized event types' do
        sendgrid_params[:_json].first[:event] = 'unrecognized'

        post(api_sendgrid_webhooks_path, params: sendgrid_params, as: :json, headers:)

        expected_event = {
          name: 'Api::SendgridWebhooksController',
          meta: {
            event: sendgrid_params[:_json].first[:event],
            message_id: sendgrid_params[:_json].first[:message_id],
            payload: sendgrid_params[:_json].first.to_json
          },
          reason: "Unsupported event type: #{sendgrid_params[:_json].first[:event]}",
          success: false
        }

        expect(DatadogSpanTagger).to have_received(:add_event_tags_to_span).with('AuditEvent', expected_event)
      end
    end

    context 'when an OAuth token is included in the request headers' do
      let(:headers) { { 'Authorization' => 'Bearer oauth-token' } }
      let(:decoded_token) { instance_double(Auth::DecodedToken, type: 'oauth2', data: {}) }

      before do
        allow(Auth::DecodeJwt).to receive(:call).with(token: 'oauth-token').and_return(decoded_token)
      end

      it_behaves_like 'a SendGrid webhook request'
    end

    context 'when no authentication or authorization is attached to the request' do
      it 'returns an unauthorized response' do
        post(api_sendgrid_webhooks_path, params: sendgrid_params, as: :json, headers:)

        expect(response).to have_http_status(:unauthorized)
        expect(response.body).to include('Authorization header can\'t be blank')
      end
    end

    context 'when an invalid authentication or authorization header is attached to the request' do
      let(:headers) { { 'Authorization' => 'Bearer test-123' } }

      it 'returns a forbidden response' do
        post(api_sendgrid_webhooks_path, params: sendgrid_params, as: :json, headers:)

        expect(response).to have_http_status(:forbidden)
        expect(response.body).to include('Invalid JWT token')
      end
    end
  end
end
