# frozen_string_literal: true

# This file is copied to spec/ when you run 'rails generate rspec:install'
unless ENV['SIMPLECOV']&.casecmp('OFF')&.zero?
  require 'simplecov'
  SimpleCov.minimum_coverage 100.0
  SimpleCov.start 'rails' do
    add_filter '/spec'

    # rails-generated superclass files
    add_filter '/app/channels/application_cable/channel.rb'
    add_filter '/app/channels/application_cable/connection.rb'
    add_filter '/app/jobs/application_job.rb'
    add_filter '/app/mailers/application_mailer.rb'
    add_filter '/app/models/application_record.rb'

    add_filter '/lib/template_seed_data.rb'
  end
end
require 'spec_helper'
ENV['RAILS_ENV'] ||= 'test'

# Prevent running tests when the Redis URL points to an external server (e.g. the staging or sandbox Redis server).
# Doing so has the potential to unexpectedly delete all Flipper flags in a shared environment.
permitted_redis_hosts = %w[localhost host.docker.internal]
%w[CLUSTERED_REDIS_URI DEDICATED_REDIS_URI].each do |redis_variable|
  next unless ENV[redis_variable] && permitted_redis_hosts.none? { |hostname| ENV[redis_variable].include?(hostname) }

  raise "## WARNING ##\nThe #{redis_variable} environment variable points to an external server " \
        "(#{ENV.fetch(redis_variable, nil)}). Update this environment variables to point to ote one of the following " \
        "hosts:\n  - #{permitted_redis_hosts.join("\n  - ")}"
end

require_relative '../config/environment'
# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production?
require 'rspec/rails'
# Add additional requires below this line. Rails is not loaded until this point!
require 'sidekiq/testing'

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
Sidekiq::Testing.fake!
Dir[Rails.root.join('spec', 'support', '**', '*.rb')].each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  abort e.to_s.strip
end
RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true

  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, type: :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://rspec.info/features/6-0/rspec-rails
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also be filtered via:
  # config.filter_gems_from_backtrace("gem name")

  config.after(:each) do
    Sidekiq::Worker.clear_all
  end
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end
