# frozen_string_literal: true

FactoryBot.define do
  factory :message_detail_info, class: Message::DetailInfo do
    delivery_method { nil }
    recipient { nil }
    status { 'PENDING' }
    inputs { {} }
    entities { build_list(:message_entity, 1) }
    trait :sms do
      delivery_method { 'SMS' }
      recipient { '1-630-278-8917' }
    end
    trait :email do
      delivery_method { 'EMAIL' }
      recipient { '<EMAIL>' }
    end
  end
end
