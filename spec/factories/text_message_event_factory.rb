# frozen_string_literal: true

FactoryBot.define do
  factory :text_message_event do
    text_message
    status_code { 1 }
    payload do
      {
        Type: 'MessageStatus',
        Payload: {
          Message: 'test message',
          Msisdn: '1-888-888-8888',
          DeliveredTime: '2023-06-09T19:58:10.6012211Z',
          StatusCode: '100',
          StatusCodeDescription: 'Delivered',
          MessageId: SecureRandom.uuid
        }
      }
    end
  end
end
