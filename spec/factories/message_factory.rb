# frozen_string_literal: true

FactoryBot.define do
  factory :message do
    id { SecureRandom.uuid }
    template
    template_version
    delivery_type { 'TextMessage' }
    delivery_id { SecureRandom.uuid }
    detail_info { build(:message_detail_info) }
    trait :sms do
      delivery { build(:text_message) }
      detail_info { build(:message_detail_info, :sms) }
    end
    trait :email do
      delivery { build(:email) }
      detail_info { build(:message_detail_info, :email) }
    end
  end
end
