# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InCentralTime do
  include ActiveSupport::Testing::TimeHelpers

  describe '.now' do
    it 'returns the current time in the central time zone during daylight savings time' do
      travel_to('2022-03-14T11:00:00Z'.to_time)

      now = InCentralTime.now

      expect(now.hour).to eq(6)
      expect(now.zone).to eq('CDT')
    end

    it 'returns the current time in the central time zone outside of daylight savings time' do
      travel_to('2022-11-07T11:00:00Z'.to_time)

      now = InCentralTime.now

      expect(now.hour).to eq(5)
      expect(now.zone).to eq('CST')
    end
  end

  describe '.date' do
    it 'returns the current date in the central time zone properly during daylight savings time' do
      travel_to('2022-03-14T04:59:59Z'.to_time)
      expect(InCentralTime.date).to eq('2022-03-13'.to_date)

      travel_to('2022-03-14T05:00:01Z'.to_time)
      expect(InCentralTime.date).to eq('2022-03-14'.to_date)
    end

    it 'returns the current date in the central time zone properly outside of daylight savings time' do
      travel_to('2022-11-07T05:59:59Z'.to_time)
      expect(InCentralTime.date).to eq('2022-11-06'.to_date)

      travel_to('2022-11-07T06:00:01Z'.to_time)
      expect(InCentralTime.date).to eq('2022-11-07'.to_date)
    end
  end

  describe '.at_noon' do
    it 'returns noon for the central time zone during daylight savings time' do
      travel_to('2022-03-14'.to_date)

      noon = InCentralTime.at_noon

      expect(noon.hour).to eq(12)
      expect(noon.zone).to eq('CDT')
    end

    it 'returns noon for the central time zone outside of daylight savings time' do
      travel_to('2022-11-07'.to_date)

      noon = InCentralTime.at_noon

      expect(noon.hour).to eq(12)
      expect(noon.zone).to eq('CST')
    end
  end

  describe '.utc_noon_hour' do
    it 'returns the correct central time hour in UTC during daylight savings time' do
      travel_to('2022-03-14'.to_date)

      expect(InCentralTime.utc_noon_hour).to eq(17)
    end

    it 'returns the correct central time hour in UTC outside of daylight savings time' do
      travel_to('2022-11-07'.to_date)

      expect(InCentralTime.utc_noon_hour).to eq(18)
    end
  end
end
