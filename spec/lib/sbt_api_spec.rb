# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SbtApi do
  let(:memory_store) { ActiveSupport::Cache.lookup_store(:memory_store) }
  let(:auth_header_cache_key) { 'sbt_api_authorization' }

  subject { described_class }

  before do
    allow(Rails).to receive(:cache).and_return(memory_store)
  end

  shared_examples 'an authorization caching call' do
    it 'generates and caches a new access token if none is found in the cache' do
      expected_headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
      expected_params = 'grant_type=client_credentials&client_id=sbt-test-client-id&client_secret=sbt-test-client-secret'
      auth_request = stub_request(:post, %r{https://login-test.solutionsbytext.com/connect/token}).with(headers: expected_headers, body: expected_params)
                                                                                                  .and_return({ body: { access_token: 'test1234', token_type: 'Bearer' }.to_json })

      api_request.with(headers: { 'Authorization' => 'Bearer test1234' }).and_return({ body: response_payload.to_json })

      Rails.cache.clear

      subject

      expect(auth_request).to have_been_requested
      expect(api_request).to have_been_requested
      expect(Rails.cache.read(auth_header_cache_key)).to eq('Bearer test1234')
    end

    it 'uses the cached access token if one is available' do
      Rails.cache.write(auth_header_cache_key, 'Bearer test9876')
      api_request.with(headers: { 'Authorization' => 'Bearer test9876' }).and_return({ body: response_payload.to_json })

      subject

      expect(api_request).to have_been_requested
    end
  end

  describe 'valid_phone_number?' do
    let(:target_api_url) { "https://t2c-api-test.solutionsbytext.com/phonenumbers-data?msisdn=#{phone_number}" }
    let(:api_request) { stub_request(:get, target_api_url) }
    let(:phone_number) { "1#{Faker::Number.number(digits: 10)}" }
    let(:response_payload) do
      {
        message: 'Request successful.',
        isError: false,
        appCode: 'gen.1200',
        data: [
          {
            msisdn: phone_number,
            type: 'Mobile',
            carrier: { code: '119684', name: 'BANDWIDTH.COM CLEC, LLC' }
          }
        ]
      }
    end

    subject { described_class.valid_phone_number?(phone_number) }

    it_behaves_like 'an authorization caching call'

    context 'when authorization is cached' do
      let(:test_auth_header) { 'Bearer testToken123' }

      before do
        Rails.cache.write(auth_header_cache_key, test_auth_header)
      end

      it 'sends an appropriate GET request to the Solutions By Text API' do
        expected_headers = { 'Authorization' => test_auth_header, 'Content-Type' => 'application/json' }
        api_request.with(headers: expected_headers).and_return({ body: response_payload.to_json })

        subject

        expect(api_request).to have_been_requested
      end

      it 'raises an error if the SBT API returns an error' do
        api_request.and_return({ status: 500, body: 'Internal Server Error' })
        expect { subject }.to raise_error(SbtApi::ResponseError, /Received 500 response/i) do |error|
          expect(error.message).to include('Internal Server Error')
        end
      end

      it 'returns true if the specified phone number is a valid mobile number' do
        api_request.and_return({ body: response_payload.to_json })
        expect(subject).to be_truthy
      end

      it 'returns false if the specified phone number is a landline number' do
        response_payload[:data][0][:type] = 'Landline'
        api_request.and_return({ body: response_payload.to_json })
        expect(subject).to be_falsey
      end

      it 'returns false if the specified phone number is an invalid phone number' do
        response_payload[:data][0][:type] = 'Invalid'
        api_request.and_return({ body: response_payload.to_json })
        expect(subject).to be_falsey
      end
    end
  end

  describe 'get_status' do
    let(:target_api_url) { "https://t2c-api-test.solutionsbytext.com/groups/sbt-test-group-id/subscribers/status?msisdn=#{phone_number}" }
    let(:api_request) { stub_request(:get, target_api_url) }
    let(:phone_number) { "1#{Faker::Number.number(digits: 10)}" }
    let(:response_payload) do
      {
        message: 'Request successful.',
        isError: false,
        appCode: 'gen.1200',
        data: [
          {
            msisdn: phone_number,
            status: 'Active',
            carrierName: 'BANDWIDTH.COM CLEC, LLC'
          }
        ]
      }
    end

    subject { described_class.get_status(phone_number) }

    it_behaves_like 'an authorization caching call'

    context 'when authorization is cached' do
      let(:test_auth_header) { 'Bearer testToken123' }

      before do
        Rails.cache.write(auth_header_cache_key, test_auth_header)
      end

      it 'sends an appropriate GET request to the Solutions By Text API' do
        expected_headers = { 'Authorization' => test_auth_header, 'Content-Type' => 'application/json' }
        api_request.with(headers: expected_headers).and_return({ body: response_payload.to_json })

        subject

        expect(api_request).to have_been_requested
      end

      it 'raises an error if the SBT API returns an error' do
        api_request.and_return({ status: 500, body: 'Internal Server Error' })
        expect { subject }.to raise_error(SbtApi::ResponseError, /Received 500 response/i) do |error|
          expect(error.message).to include('Internal Server Error')
        end
      end

      it 'returns a payload with a valid status from SBT' do
        api_request.and_return({ body: response_payload.to_json })
        expect(subject).to eq(response_payload.as_json)
      end

      it 'returns a status of Active if the specified phone number is subscribed' do
        api_request.and_return({ body: response_payload.to_json })
        expect(subject.dig('data', 0, 'status')).to eq('Active')
      end

      it 'returns a status of "not a subscriber" if the specified phone number is neither subscribed or opted out' do
        response_payload[:data][0][:status] = 'Not a subscriber'
        api_request.and_return({ body: response_payload.to_json })
        expect(subject.dig('data', 0, 'status')).to eq('Not a subscriber')
      end

      it 'returns a status of InActive if the specified phone number has opted out' do
        response_payload[:data][0][:status] = 'InActive'
        api_request.and_return({ body: response_payload.to_json })
        expect(subject.dig('data', 0, 'status')).to eq('InActive')
      end
    end
  end

  describe 'add_subscriber!' do
    let(:target_api_url) { 'https://t2c-api-test.solutionsbytext.com/groups/sbt-test-group-id/subscribers' }
    let(:api_request) { stub_request(:post, target_api_url) }
    let(:phone_number) { "1#{Faker::Number.number(digits: 10)}" }
    let(:response_payload) do
      {
        message: 'Request successful',
        isError: false,
        appCode: 'gen.1200',
        data: {
          messageId: 'a0b2acf5-fabd-4124-bcd2-4329184ab09a',
          msisdn: phone_number
        }
      }
    end

    subject { described_class.add_subscriber!(phone_number) }

    it_behaves_like 'an authorization caching call'

    context 'when authorization is cached' do
      let(:test_auth_header) { 'Bearer testToken123' }

      before do
        Rails.cache.write(auth_header_cache_key, test_auth_header)
      end

      it 'sends an appropriate POST request to the Solutions By Text API' do
        expected_headers = { 'Authorization' => test_auth_header, 'Content-Type' => 'application/json' }
        api_request.with(headers: expected_headers).and_return({ body: response_payload.to_json })

        subject

        expect(api_request).to have_been_requested
      end

      it 'raises an error if the SBT API returns an error' do
        api_request.and_return({ status: 500, body: 'Internal Server Error' })
        expect { subject }.to raise_error(SbtApi::ResponseError, /Received 500 response/i) do |error|
          expect(error.message).to include('Internal Server Error')
        end
      end

      it 'returns if the specified phone number is successfully subscribed' do
        api_request.and_return({ body: response_payload.to_json })
        subject
      end

      it 'returns if the specified phone number is already an existing subscriber' do
        existing_subscription_response_payload = {
          message: 'Subscriber exist',
          isError: true,
          appCode: 'sub.1019'
        }
        api_request.and_return({ status: 409, body: existing_subscription_response_payload.to_json })
        subject
      end

      it 'raises an error if the subscription request fails' do
        error_response_payload = {
          message: 'Access Forbidden',
          isError: true,
          appCode: 'gen.1403'
        }
        api_request.and_return({ status: 200, body: error_response_payload.to_json })
        expect { subject }.to raise_error(SbtApi::ResponseError, /Failed to add new subscriber/i)
      end
    end
  end

  describe 'send_message!' do
    before do
      allow(Rails.env).to receive(:production?).and_return(true)
      allow(Flipper).to receive(:enabled?).with(:disable_sending_sms).and_return(false)
    end
    let(:target_api_url) { 'https://t2c-api-test.solutionsbytext.com/groups/sbt-test-group-id/messages' }
    let(:api_request) { stub_request(:post, target_api_url) }
    let(:phone_number) { "1#{Faker::Number.number(digits: 10)}" }
    let(:message_body) { Faker::Lorem.sentence }
    let(:sbt_message_id) { SecureRandom.uuid }
    let(:response_payload) do
      {
        data: {
          messageId: sbt_message_id
        },
        appCode: 'gen.1200',
        message: 'Request successful',
        isError: false
      }
    end

    subject { described_class.send_message!(phone_number, message_body) }

    it_behaves_like 'an authorization caching call'

    context 'when authorization is cached' do
      let(:test_auth_header) { 'Bearer testToken123' }

      before do
        Rails.cache.write(auth_header_cache_key, test_auth_header)
      end

      it 'sends an appropriate POST request to the Solutions By Text API' do
        expected_headers = { 'Authorization' => test_auth_header, 'Content-Type' => 'application/json' }
        expected_request_body = { message: message_body, messageType: 'Unicast', subscribers: [{ msisdn: phone_number }] }
        api_request.with(headers: expected_headers, body: expected_request_body.to_json).and_return({ body: response_payload.to_json })

        subject

        expect(api_request).to have_been_requested
      end

      it 'raises an error if the SBT API returns an error' do
        api_request.and_return({ status: 500, body: 'Internal Server Error' })
        expect { subject }.to raise_error(SbtApi::ResponseError, /Received 500 response/i) do |error|
          expect(error.message).to include('Internal Server Error')
        end
      end

      it 'returns the resulting SBT message ID if one is included in the response' do
        api_request.and_return({ body: response_payload.to_json })
        expect(subject).to eq(sbt_message_id)
      end

      it 'raises an error if no SBT message ID is included in the response' do
        response_payload[:data][:messageId] = nil
        api_request.and_return({ body: response_payload.to_json })
        expect { subject }.to raise_error(SbtApi::ResponseError, /Failed to trigger delivery of SMS message/i)
      end
    end

    context 'when called in lower env and disable_sending_sms is enabled' do
      before do
        allow(Rails.env).to receive(:production?).and_return(false)
        allow(Flipper).to receive(:enabled?).with(:disable_sending_sms).and_return(true)
      end
      it 'returns a mocked response' do
        subject

        expect(api_request).not_to have_been_requested
      end
    end
  end

  describe '.normalized_url' do
    it 'leaves an existing trailing slash in place if it is present' do
      url = 'https://t2c-api-test.solutionsbytext.com/test/123/'
      expect(described_class.send(:normalized_url, url)).to eq(url)
    end

    it 'adds a trailing slash in place if it is present' do
      url = 'https://t2c-api-test.solutionsbytext.com/test/123'
      expect(described_class.send(:normalized_url, url)).to eq("#{url}/")
    end

    it 'leaves duplicated slashes within the path of the URL unchanged' do
      url = 'https://t2c-api-test.solutionsbytext.com/test//123/'
      expect(described_class.send(:normalized_url, url)).to eq(url)
    end

    it 'leaves duplicated slashes at the end of the path when present' do
      url = 'https://t2c-api-test.solutionsbytext.com/test/123//'
      expect(described_class.send(:normalized_url, url)).to eq(url)
    end
  end
end
