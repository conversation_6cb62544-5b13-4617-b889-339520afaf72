# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TemplateVariables, type: :model do
  it 'includes supported variables' do
    supported_variables = %i[
      address_line1
      address_line2
      address_city
      address_state
      address_zip_code
      applicant_address
      applicant_city_state_zip
      applicant_name
      auto_payment_type
      bank_name
      code
      contract_date
      credit_report_date
      date
      days_past_due
      display_oh_discrimination_disclosure
      email
      expiration_date
      factors
      failure_date
      first_name
      first_payment_on
      loan_maturity_date
      full_due_amount
      full_name
      gds_decline_reason
      gds_decline_reasons
      gds_score
      investor_name
      is_crb
      is_equifax
      is_fcra
      itemization_date_from_contract_date
      itemization_date_from_payment_date
      last_four
      last_name
      last_payment_amount
      last_payment_due_date
      link
      loan_agreement_file
      loan_agreement_files
      loan_display_id
      loan_term
      lp_loan_status
      message_send_date
      message_send_date_plus28
      message_send_date_plus29
      message_send_date_plus30
      next_payment_date
      next_scheduled_payment_amount
      offer_amount
      offers
      originator_name
      past_payment_amounts_list
      past_payment_dates_list
      past_payment_list
      payment_amount
      payment_date
      payment_frequency
      phone_number
      return_code
      service_entity_name
      service_entity_shortcode
      subject
      token
      total_amount_due
      total_credited
      total_interest_due
      total_payoff_amount
      total_payoff_amount_less_interest
      unified_id
      charge_off_date
      charge_off_balance
      balance
      sale_date
      last_4_unified_id
    ]

    expect(TemplateVariables::VARIABLES.keys).to match_array(supported_variables)
  end

  it 'has valid variables' do
    all_template_variables = TemplateVariables::VARIABLES.values.all? { |variable| variable.is_a?(TemplateVariable) }
    invalidated = TemplateVariables::VARIABLES.values.filter_map { |variable| variable.name unless variable.valid? }

    expect(all_template_variables).to eq(true)
    expect(invalidated).to be_empty
  end
end
