# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TextMessageScheduler::ServicingScheduler do
  include ActiveSupport::Testing::TimeHelpers

  let(:message) { create(:message, :sms) }

  describe 'window setting' do
    it 'defaults the window when window keys are not present' do
      stub_const(
        'ENV',
        ENV.except('SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR', 'SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR')
      )

      expect(TextMessageScheduler::ServicingScheduler.from_hour).to eq(11)
      expect(TextMessageScheduler::ServicingScheduler.until_hour).to eq(18)
    end

    it 'defaults the window when window keys set to nil' do
      stub_const(
        'ENV',
        ENV.to_h.merge('SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR' => nil, 'SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => nil)
      )

      expect(TextMessageScheduler::ServicingScheduler.from_hour).to eq(11)
      expect(TextMessageScheduler::ServicingScheduler.until_hour).to eq(18)
    end

    it 'allows window to be set' do
      stub_const(
        'ENV',
        ENV.to_h.merge('SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR' => '14', 'SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => '16')
      )

      expect(TextMessageScheduler::ServicingScheduler.from_hour).to eq(14)
      expect(TextMessageScheduler::ServicingScheduler.until_hour).to eq(16)
    end

    it 'may be configured such that any hour is schedulable' do
      stub_const(
        'ENV',
        ENV.to_h.merge('SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR' => '0', 'SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => '24')
      )

      24.times do |hour|
        expect(TextMessageScheduler::ServicingScheduler.within_schedule?(hour)).to eq(true), "Unschedulable on hour #{hour}"
      end
    end
  end

  describe '#call' do
    before do
      stub_const(
        'ENV',
        ENV.to_h.merge(
          'SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR' => 10,
          'SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => 20
        )
      )
    end

    context 'when within window' do
      it 'enqueues a message to be sent immediately' do
        travel_to(InCentralTime.at_noon)

        expect do
          TextMessageScheduler::ServicingScheduler.call(message:)
        end.to enqueue_sidekiq_job(SendTextMessageJob).with(message.id).immediately

        message.reload
        expect(message.detail_info.schedule_at).to eq(InCentralTime.at_noon)
      end
    end

    context 'when before hours' do
      it 'schedules the message for just after allowed hours' do
        travel_to(InCentralTime.now.change(hour: 6))
        earliest_time = InCentralTime.now.change(hour: TextMessageScheduler::ServicingScheduler.from_hour)
        latest_time = earliest_time + 1.hour

        expect do
          TextMessageScheduler::ServicingScheduler.call(message:)
        end.to enqueue_sidekiq_job(SendTextMessageJob)

        message.reload
        expect(message.detail_info.schedule_at).to be > earliest_time
        expect(message.detail_info.schedule_at).to be < latest_time
      end
    end

    context 'when after hours' do
      it 'schedules the message to be sent within the next available allowed hours window' do
        travel_to(InCentralTime.now.change(hour: 21))
        earliest_time = (InCentralTime.now + 1.day).change(hour: TextMessageScheduler::ServicingScheduler.from_hour)
        latest_time = earliest_time + 1.hour

        expect do
          TextMessageScheduler::ServicingScheduler.call(message:)
        end.to enqueue_sidekiq_job(SendTextMessageJob)

        message.reload
        expect(message.detail_info.schedule_at).to be > earliest_time
        expect(message.detail_info.schedule_at).to be < latest_time
      end
    end
  end
end
