# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AttributeFormatter do
  describe '#call' do
    it 'formats money amount' do
      [
        { unformatted: 374.18, formatted: '374.18' },
        { unformatted: 120, formatted: '120.00' },
        { unformatted: 120.1, formatted: '120.10' },
        { unformatted: '120.1', formatted: '120.10' },
        { unformatted: 120.40, formatted: '120.40' },
        { unformatted: 120.00, formatted: '120.00' }
      ].each do |t|
        expect(AttributeFormatter.call(payment_amount: t[:unformatted])).to include('payment_amount' => t[:formatted])
      end
    end

    it 'formats a date' do
      expect(AttributeFormatter.call(first_payment_on: '2022-01-05')).to include('first_payment_on' => '01/05/2022')
    end

    it 'includes message constants' do
      message_constants = {
        'CUSTOMER_SERVICE_PHONE_NUMBER' => '************',
        'TEXT_MESSAGE_OPT_OUT' => 'Reply STOP to opt out.'
      }
      expect(AttributeFormatter.call({})).to include(message_constants)
    end

    it 'preserves other attributes' do
      formatted_attributes = AttributeFormatter.call(
        email: '<EMAIL>',
        phone_number: '15557778888',
        first_name: 'Blerica',
        loan_term: 12.1
      )

      expect(formatted_attributes).to include(
        'email' => '<EMAIL>',
        'phone_number' => '15557778888',
        'first_name' => 'Blerica',
        'loan_term' => 12.1
      )
    end

    it 'does not include variables of file type' do
      formatted_attributes = AttributeFormatter.call(
        email: '<EMAIL>',
        loan_agreement_file: {}
      )

      expect(formatted_attributes).not_to include('loan_agreement_file')
    end
  end
end
