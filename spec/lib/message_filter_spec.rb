# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MessageFilter do
  include ActiveSupport::Testing::TimeHelpers

  let!(:past_due_template) { create(:template, detail_info: build(:template_detail_info, key: 'payment_past_due_5_days')) }
  let!(:noaa_template) { create(:template, detail_info: build(:template_detail_info, key: 'notice_of_adverse_action')) }
  let!(:template) { create(:template) }

  let(:borrower_entity) { { id: 'abc123', type: 'BORROWER' } }
  let(:application_entity) { { id: 'abc123', type: 'APPLICATION' } }
  let(:detail_info1) do
    build(
      :message_detail_info,
      :email,
      status: 'DELIVERED',
      recipient: '<EMAIL>',
      inputs: {
        first_name: 'Test 1',
        payment_amount: 100.29
      },
      entities: [borrower_entity]
    )
  end
  let(:detail_info2) do
    build(
      :message_detail_info,
      :sms,
      status: 'TRIGGERED',
      recipient: '1-847-888-8888',
      inputs: {
        first_name: 'Test 2',
        payment_amount: 120.45
      },
      entities: [borrower_entity, application_entity]
    )
  end
  let(:detail_info3) do
    build(
      :message_detail_info,
      :sms,
      status: 'DELIVERED',
      recipient: '1-888-888-8888',
      inputs: {
        first_name: 'Test 3',
        payment_amount: 130.45
      },
      entities: [application_entity]
    )
  end
  let(:detail_info4) do
    build(
      :message_detail_info,
      :email,
      status: 'PENDING',
      recipient: '<EMAIL>',
      inputs: {
        first_name: 'Test 4',
        payment_amount: 140.45
      },
      entities: []
    )
  end

  let!(:message1) { create(:message, detail_info: detail_info1, template:) }
  let!(:message2) { create(:message, detail_info: detail_info2, template: noaa_template) }
  let!(:message3) { create(:message, detail_info: detail_info3, template:) }
  let!(:message4) { create(:message, detail_info: detail_info4, template: past_due_template) }

  describe '#call' do
    subject { described_class.new(filters) }

    context 'when filtering by status' do
      let(:filters) { { status: 'DELIVERED' } }

      it 'returns the messages with the matching status' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message3)
      end
    end

    context 'when filtering by statuses' do
      let(:filters) { { statuses: %w[DELIVERED TRIGGERED] } }

      it 'returns the messages with any of the specified status' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message2, message3)
      end
    end

    context 'when filtering status negation' do
      let(:filters) { { not_status: 'DELIVERED' } }

      it 'returns the messages that do NOT have the specified status' do
        expect(subject.call(Message.all)).to contain_exactly(message2, message4)
      end
    end

    context 'when filtering by recipient' do
      let(:filters) { { recipient: '<EMAIL>' } }

      it 'returns the messages with the matching recipient' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message4)
      end
    end

    context 'when filtering by recipients' do
      let(:filters) { { recipients: ['<EMAIL>', '1-888-888-8888'] } }

      it 'returns the messages with any of the specified recipients' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message3, message4)
      end
    end

    context 'when filtering recipient negation' do
      let(:filters) { { not_recipient: '<EMAIL>' } }

      it 'returns the messages that do NOT have the specified recipient' do
        expect(subject.call(Message.all)).to contain_exactly(message2, message3)
      end
    end

    context 'when filtering by delivery_method' do
      let(:filters) { { delivery_method: 'SMS' } }

      it 'returns the messages with the matching delivery_method' do
        expect(subject.call(Message.all)).to contain_exactly(message2, message3)
      end
    end

    context 'when filtering by delivery_methods' do
      let(:filters) { { delivery_methods: %w[SMS EMAIL] } }

      it 'returns the messages with any of the specified delivery methods' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message2, message3, message4)
      end
    end

    context 'when filtering delivery_method negation' do
      let(:filters) { { not_delivery_method: 'SMS' } }

      it 'returns the messages do NOT have the specified delivery method' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message4)
      end
    end

    context 'when filtering by template_key' do
      let(:filters) { { template_key: 'payment_past_due_5_days' } }

      it 'returns the messages with the matching template_key' do
        expect(subject.call(Message.all)).to contain_exactly(message4)
      end
    end

    context 'when filtering by template_keys' do
      let(:filters) { { template_keys: %w[payment_past_due_5_days notice_of_adverse_action] } }

      it 'returns the messages any of the specified template keys' do
        expect(subject.call(Message.all)).to contain_exactly(message2, message4)
      end
    end

    context 'when filtering by attribution_id' do
      let(:filters) { { attribution_id: 'abc123' } }

      it 'returns the messages with the attribution id' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message2, message3)
      end
    end

    context 'when filtering by attribution_type' do
      let(:filters) { { attribution_type: 'borrower' } }

      it 'returns the messages with the attribution type' do
        expect(subject.call(Message.all)).to contain_exactly(message1, message2)
      end
    end

    context 'when filtering by attribution_id and attribution_type' do
      let(:filters) { { attribution_id: 'abc123', attribution_type: 'application' } }

      it 'returns the messages with the attribution type' do
        expect(subject.call(Message.all)).to contain_exactly(message2, message3)
      end
    end

    context 'when filtering by timestamp attributes' do
      before do
        freeze_time

        message1.update(created_at: 4.days.ago, updated_at: 1.hours.ago)
        message2.update(created_at: 3.days.ago, updated_at: 2.hours.ago)
        message3.update(created_at: 2.days.ago, updated_at: 3.hours.ago)
        message4.update(created_at: 1.days.ago, updated_at: 4.hours.ago)
      end

      context 'when filtering on the created before condition' do
        let(:filters) { { created_before: 60.hours.ago } }

        it 'returns the messages any of the specified template keys' do
          expect(subject.call(Message.all)).to contain_exactly(message1, message2)
        end
      end

      context 'when filtering on the created after condition' do
        let(:filters) { { created_after: 80.hours.ago } }

        it 'returns the messages any of the specified template keys' do
          expect(subject.call(Message.all)).to contain_exactly(message2, message3, message4)
        end
      end

      context 'when filtering on the updated before condition' do
        let(:filters) { { updated_before: 210.minutes.ago } }

        it 'returns the messages any of the specified template keys' do
          expect(subject.call(Message.all)).to contain_exactly(message4)
        end
      end

      context 'when filtering on the updated after condition' do
        let(:filters) { { updated_after: 210.minutes.ago } }

        it 'returns the messages any of the specified template keys' do
          expect(subject.call(Message.all)).to contain_exactly(message1, message2, message3)
        end
      end
    end

    context 'when combining multiple filters' do
      let(:filters) { { template_key: 'payment_past_due_5_days', recipient: '<EMAIL>' } }

      it 'returns the messages with the matching template_key' do
        expect(subject.call(Message.all)).to contain_exactly(message4)
      end
    end
  end

  describe '#valid?' do
    subject { described_class.new(filters) }

    context 'when an unsupported filter is used' do
      let(:filters) { { foo: 'bar' } }

      it 'returns false and populates an error message' do
        expect(subject.valid?).to eq(false)
        expect(subject.errors).to include({ message: 'foo is not a supported filter' })
      end
    end

    context 'when a supported filter is used' do
      let(:filters) { { template_key: 'payment_past_due_5_days' } }

      it 'returns true' do
        expect(subject.valid?).to eq(true)
      end
    end

    context 'when multiple supported filter are used' do
      let(:filters) { { delivery_methods: %w[EMAIL SMS], created_before: '2024-05-01' } }

      it 'returns true' do
        expect(subject.valid?).to eq(true)
      end
    end

    context 'when one of the provided filter is unsupported' do
      let(:filters) { { status: 'TRIGGERED', invalid: 'testing', template_key: 'payoff' } }

      it 'returns false and populates an error message' do
        expect(subject.valid?).to eq(false)
        expect(subject.errors).to include({ message: 'invalid is not a supported filter' })
      end
    end

    context 'when multiple of the provided filters are unsupported' do
      let(:filters) { { status: 'TRIGGERED', testing: '1234', template_key: 'payoff', unknown: 'true' } }

      it 'returns false and populates an error message for each unsupported filter' do
        expect(subject.valid?).to eq(false)
        expect(subject.errors).to include({ message: 'testing is not a supported filter' })
        expect(subject.errors).to include({ message: 'unknown is not a supported filter' })
      end
    end
  end
end
