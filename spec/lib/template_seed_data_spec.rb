# frozen_string_literal: true

require 'rails_helper'
require 'template_seed_data'

RSpec.describe 'TemplateSeedData' do
  def active_template_version_for(key:)
    template = Template.find_by("detail_info->>'key' = :key", { key: })
    active_template_versions = template.template_versions.where("detail_info->>'status' = :status", { status: TemplateVersion::DetailInfo::ACTIVE_STATUS })

    raise StandardError, 'Unexpected number of active template versions' if active_template_versions.size != 1

    active_template_versions.first
  end

  describe '.seed_emails!' do
    it 'seeds templates in non-production environments' do
      TemplateSeedData.seed_emails!

      Template.all.each do |template|
        # Expects that every template defined in seeds ends up with a vendor id from the sendgrid.yml config
        expect(active_template_version_for(key: template.detail_info.key).detail_info.vendor_id).to be_present
      end
    end

    it 'seeds templates in the production environment' do
      environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: :production, production?: true)
      expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

      TemplateSeedData.seed_emails!

      Template.all.each do |template|
        # Expects that every template defined in seeds ends up with a vendor id from the sendgrid.yml config
        expect(active_template_version_for(key: template.detail_info.key).detail_info.vendor_id).to be_present
      end
    end

    it 'sets the correct DEFAULT values for optional detail_info attributes' do
      TemplateSeedData.seed_emails!

      bbb_consumer_review_template = Template.find_by("detail_info->>'key' = :key", key: 'bbb_consumer_review')

      expect(bbb_consumer_review_template.detail_info.limit_per_borrower).to eq(nil)
      expect(bbb_consumer_review_template.detail_info.override_suppression).to eq(false)
      expect(bbb_consumer_review_template.detail_info.attach_vcf_card).to eq(false)
    end

    it 'sets the correct values for optional detail_info attributes' do
      TemplateSeedData.seed_emails!

      offer_expiration_retargeting_social_proof_template = Template.find_by("detail_info->>'key' = :key", key: 'offer_expiration_retargeting_social_proof')
      identity_welcome_template = Template.find_by("detail_info->>'key' = :key", key: 'identity_welcome')

      expect(offer_expiration_retargeting_social_proof_template.detail_info.limit_per_borrower).to eq(2)
      expect(identity_welcome_template.detail_info.override_suppression).to eq(true)
      expect(identity_welcome_template.detail_info.attach_vcf_card).to eq(true)
    end
  end
end
