# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DatadogSpanTagger do
  let(:span) { Datadog::Tracing::Span.new('test-span') }

  before do
    allow(Datadog::Tracing).to receive(:active_span).and_return(span)
    allow(Datadog::Tracing).to receive(:trace).and_yield(span)
    allow(Datadog::Tracing).to receive(:keep!)
    allow(span).to receive(:set_tags)
    allow(span).to receive(:set_error)
  end

  describe '.add_event_tags_to_span' do
    let(:event_name) { 'SendEmailJob' }
    let(:payload) do
      {
        ip_address: '************',
        name: event_name,
        success: true,
        meta: {
          agent: :admin,
          status_code: '200'
        }
      }
    end

    context 'when the event type is RequestEvent or AsyncEvent' do
      let(:event_type) { [DatadogSpanTagger::REQUEST_EVENT, DatadogSpanTagger::ASYNC_EVENT].sample }

      it 'sets tags on the active span for the event type and all payload attributes' do
        described_class.add_event_tags_to_span(event_type, payload)

        expect(Datadog::Tracing).to have_received(:active_span)
        expect(span).to have_received(:set_tags).with(
          {
            'event.type' => event_type,
            'event.ip_address' => '************',
            'event.name' => event_name,
            'event.success' => true,
            'event.meta.agent' => :admin,
            'event.meta.status_code' => '200'
          }
        )
        expect(Datadog::Tracing).to have_received(:keep!)
      end

      context 'when there is no active span' do
        before do
          allow(Datadog::Tracing).to receive(:active_span).and_return(nil)
          allow(Rails.logger).to receive(:warn).and_call_original
        end

        it 'logs a message' do
          described_class.add_event_tags_to_span(event_type, payload)

          expect(Rails.logger).to have_received(:warn).with("#{described_class}: No active datadog span for #{event_type}", payload)
        end
      end
    end

    context 'for other event types' do
      let(:event_type) { [DatadogSpanTagger::API_EVENT, DatadogSpanTagger::AUDIT_EVENT].sample }
      let(:expected_span_name) { "#{event_type} - #{event_name}" }

      it 'sets tags on a new span for the event type and all payload attributes' do
        described_class.add_event_tags_to_span(event_type, payload)

        expect(Datadog::Tracing).to have_received(:trace).with(expected_span_name)
        expect(span).to have_received(:set_tags).with(
          {
            'event.type' => event_type,
            'event.ip_address' => '************',
            'event.name' => event_name,
            'event.success' => true,
            'event.meta.agent' => :admin,
            'event.meta.status_code' => '200'
          }
        )
        expect(Datadog::Tracing).to have_received(:keep!)
      end
    end
  end

  describe '.error' do
    let(:error) { RuntimeError.new('boom') }

    it 'sets the error on the active span' do
      described_class.error(error)

      expect(span).to have_received(:set_error).with(error)
    end

    context 'when there is no active span' do
      before do
        allow(Datadog::Tracing).to receive(:active_span).and_return(nil)
        allow(Rails.logger).to receive(:warn).and_call_original
      end

      it 'logs a message' do
        described_class.error(error)

        expect(Rails.logger).to have_received(:warn).with("#{described_class}: No active datadog span - unable to set error on span")
      end
    end
  end
end
