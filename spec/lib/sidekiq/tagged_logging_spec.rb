# frozen_string_literal: true

require 'rails_helper'

describe Sidekiq::TaggedLogging do
  let(:capture_logger) { SemanticLogger::Test::CaptureLogEvents.new }
  let(:job_instance) { double }
  let(:queue) { 'default' }

  describe 'Client' do
    subject { Sidekiq::TaggedLogging::Client.new }
    let(:redis_pool) { double }
    let(:named_tags) { { loan_id: SecureRandom.uuid }.stringify_keys }

    it 'adds tag_logging to the jobs payload' do
      job = { request_id: 123 }.stringify_keys
      SemanticLogger.tagged(**named_tags) do
        subject.call(job_instance, job, queue, redis_pool) do
          self
        end

        expect(job['tag_logging']).to eq(named_tags)
        expect(job['request_id']).to eq(123)
      end
    end
  end

  describe 'Server' do
    subject { Sidekiq::TaggedLogging::Server.new }
    let(:named_tags) { { loan_id: SecureRandom.uuid }.stringify_keys }

    it 'include named tags from the payload' do
      job = { tag_logging: named_tags }.stringify_keys

      subject.call(job_instance, job, queue) do
        capture_logger.info('testing')
      end

      expect(capture_logger.events.first.named_tags).to eq(named_tags)
    end
  end
end
