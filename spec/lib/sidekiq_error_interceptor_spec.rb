# frozen_string_literal: true

require 'rails_helper'

class SpecificTestError < StandardError; end

RSpec.describe SidekiqErrorInterceptor do
  describe '#call' do
    before do
      allow(Rails.logger).to receive(:warn)
    end

    it 'intercepts errors as part of the sidekiq middleware' do
      specific_exception = SpecificTestError.new('Boom!')
      expect do
        SidekiqErrorInterceptor.new.call('job_instance', 'job_payload', 'queue') do
          raise specific_exception
        end
      end.to(raise_error(SidekiqErrorInterceptor::RetryError) do |ex|
        expect(ex.message).to eq('{:job=>"String", :job_payload=>"job_payload", :queue=>"queue", :e=>#<SpecificTestError: Boom!>}')
        expect(ex.wrapped_exception).to eq(specific_exception)
      end)

      warn_args = [
        'Sidekiq job raised exception; raising RetryError for retry.',
        { e: instance_of(SpecificTestError), job: 'String', job_payload: 'job_payload', queue: 'queue' }
      ]
      expect(Rails.logger).to have_received(:warn).with(*warn_args)
    end

    it 'raises the original error when retry count indicates job will no longer retry' do
      specific_exception = SpecificTestError.new('Boom!')
      expect do
        SidekiqErrorInterceptor.new.call('job_instance', { 'retry' => 2, 'retry_count' => 1 }, 'queue') do
          raise specific_exception
        end
      end.to raise_error(specific_exception)
    end
  end
end
