# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ObservabilityEvent do
  let(:dummy_class) do
    Class.new do
      include ObservabilityEvent

      def self.name
        'Send<PERSON>mail<PERSON>ob'
      end
    end
  end

  let(:instance) { dummy_class.new }

  before do
    allow(DatadogSpanTagger).to receive(:add_event_tags_to_span)
  end

  describe '#success!' do
    it 'records a success event in datadog' do
      dummy_class.success!(detail: 'some info')

      expect(DatadogSpanTagger).to have_received(:add_event_tags_to_span).with(
        'AsyncEvent',
        hash_including(success: true, meta: { detail: 'some info' }, name: 'SendEmail<PERSON>ob')
      )
    end
  end

  describe '#failure!' do
    it 'records a failure event in datadog' do
      dummy_class.failure!(reason: 'timeout', meta: { attempt: 2 })

      expect(DatadogSpanTagger).to have_received(:add_event_tags_to_span).with(
        'AsyncEvent',
        hash_including(success: false, reason: 'timeout', meta: { attempt: 2 }, name: 'Send<PERSON><PERSON><PERSON><PERSON>')
      )
    end
  end

  context 'when the including class is not a job' do
    let(:dummy_class) do
      Class.new do
        include ObservabilityEvent

        def self.name
          'SomeController'
        end
      end
    end

    it 'records the datadog event with type AuditEvent' do
      dummy_class.failure!(reason: 'timeout', meta: { attempt: 2 })

      expect(DatadogSpanTagger).to have_received(:add_event_tags_to_span).with(
        'AuditEvent',
        hash_including(success: false, reason: 'timeout', meta: { attempt: 2 }, name: 'SomeController')
      )
    end
  end
end
