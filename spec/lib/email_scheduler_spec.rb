# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailScheduler do
  include ActiveSupport::Testing::TimeHelpers

  let(:detail_info) { build(:template_detail_info, topic: Template::DetailInfo::DELINQUENCY_TOPIC) }
  let(:message) { create(:message, :email, template: create(:template, detail_info:)) }

  describe 'window setting' do
    context 'for delinquency topic emails' do
      context 'when the config has no keys' do
        before do
          stub_const(
            'ENV',
            ENV.except('INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_FROM_HOUR', 'INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_UNTIL_HOUR')
          )
        end

        it 'uses the default window' do
          expect(EmailScheduler.new(message:).from_hour).to eq(11)
          expect(EmailScheduler.new(message:).until_hour).to eq(21)
        end
      end

      context 'when the config values are nil' do
        before do
          stub_const(
            'ENV',
            ENV.to_h.merge('INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_FROM_HOUR' => nil, 'INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => nil)
          )
        end

        it 'allows any time of day' do
          expect(EmailScheduler.new(message:).from_hour).to eq(11)
          expect(EmailScheduler.new(message:).until_hour).to eq(21)
        end
      end

      context 'when the config values are present' do
        before do
          stub_const(
            'ENV',
            ENV.to_h.merge('INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_FROM_HOUR' => '14', 'INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => '16')
          )
        end

        it 'uses the values from config' do
          expect(EmailScheduler.new(message:).from_hour).to eq(14)
          expect(EmailScheduler.new(message:).until_hour).to eq(16)
        end
      end

      context 'when the config values are 0, 24' do
        before do
          stub_const(
            'ENV',
            ENV.to_h.merge('INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_FROM_HOUR' => '0', 'INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => '24')
          )
        end

        it 'allows any time of day' do
          24.times do |hour|
            expect(EmailScheduler.new(message:).within_schedule?(hour)).to eq(true), "Unschedulable on hour #{hour}"
          end
        end
      end
    end

    context 'for topics exempt from windowing' do
      let(:detail_info) { build(:template_detail_info, topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC) }
      it 'allows any time of day' do
        expect(EmailScheduler.new(message:).from_hour).to eq(0)
        expect(EmailScheduler.new(message:).until_hour).to eq(24)
      end
    end
  end

  describe '#call' do
    before do
      stub_const(
        'ENV',
        ENV.to_h.merge(
          'INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_FROM_HOUR' => 10,
          'INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_UNTIL_HOUR' => 20
        )
      )
    end

    context 'when within window' do
      it 'enqueues a message to be sent immediately' do
        environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: :production, production?: true)
        expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

        travel_to(InCentralTime.at_noon)

        expect do
          EmailScheduler.call(message:)
        end.to enqueue_sidekiq_job(SendEmailJob).with(message.id).immediately

        message.reload
        expect(message.detail_info.schedule_at).to eq(InCentralTime.at_noon)
      end
    end

    context 'when before hours' do
      it 'schedules the message for just after allowed hours' do
        environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: :production, production?: true)
        expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

        travel_to(InCentralTime.now.change(hour: 6))
        earliest_time = InCentralTime.now.change(hour: EmailScheduler.new(message:).from_hour)
        latest_time = earliest_time + 1.hour

        expect do
          EmailScheduler.call(message:)
        end.to enqueue_sidekiq_job(SendEmailJob)

        message.reload
        expect(message.detail_info.schedule_at).to be > earliest_time
        expect(message.detail_info.schedule_at).to be < latest_time
      end
    end

    context 'when after hours' do
      it 'schedules for the next day' do
        environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: :production, production?: true)
        expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

        travel_to(InCentralTime.now.change(hour: 21))
        earliest_time = InCentralTime.now.tomorrow.change(hour: EmailScheduler.new(message:).from_hour)
        latest_time = earliest_time + 1.hour

        expect do
          EmailScheduler.call(message:)
        end.to enqueue_sidekiq_job(SendEmailJob)

        message.reload
        expect(message.detail_info.schedule_at).to be > earliest_time
        expect(message.detail_info.schedule_at).to be < latest_time
        expect(message.detail_info.status).not_to eq(Message::DetailInfo::UNSCHEDULABLE_STATUS)
      end
    end

    %i[test sandbox staging].each do |env|
      context "when in #{env} environment and after hours" do
        it 'enqueues a message to be sent immediately' do
          environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: env, production?: false)
          expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

          travel_to(InCentralTime.now.change(hour: 21))
          expect do
            EmailScheduler.call(message:)
          end.to enqueue_sidekiq_job(SendEmailJob).with(message.id).immediately

          message.reload
          expect(message.detail_info.schedule_at).to eq(InCentralTime.now.change(hour: 21))
        end
      end

      context "when in #{env} environment and before hours" do
        it 'enqueues a message to be sent immediately' do
          environment_double = instance_double('ActiveSupport::EnvironmentInquirer', to_sym: env, production?: false)
          expect(Rails).to receive(:env).at_least(:once).and_return(environment_double)

          travel_to(InCentralTime.now.change(hour: 6))
          expect do
            EmailScheduler.call(message:)
          end.to enqueue_sidekiq_job(SendEmailJob).with(message.id).immediately

          message.reload
          expect(message.detail_info.schedule_at).to eq(InCentralTime.now.change(hour: 6))
        end
      end
    end
  end
end
