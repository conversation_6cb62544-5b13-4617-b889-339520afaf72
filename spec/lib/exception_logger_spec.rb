# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ExceptionLogger do
  let(:error) { StandardError.new('boom!') }

  it 'logs with the Rails logger' do
    expect(Rails.logger).to receive(:error).with('Error raised', exception: error)
    ExceptionLogger.error(error)
  end

  it 'sets the error on the datadog span' do
    expect(DatadogSpanTagger).to receive(:error).with(error)
    ExceptionLogger.error(error)
  end
end
