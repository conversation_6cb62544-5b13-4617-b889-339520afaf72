# frozen_string_literal: true

# spec/sendgrid_api_spec.rb
require 'rails_helper'

RSpec.describe SendgridApi do
  describe '.send_message!' do
    let(:email) { '<EMAIL>' }
    let(:sg_template_id) { 'my_template_id' }
    let(:template) { build(:template) }
    let(:template_data) { { 'name' => '<PERSON>', 'amount' => '123.45' } }
    let(:message_id) { SecureRandom.uuid }
    let(:personalizations) { [SendgridApi.build_personalization(email:, template_data:, message_id:)] }

    it 'sends an email from the servicing address when the topic of the specified template is delinquent' do
      template.detail_info.topic = Template::DetailInfo::DELINQUENCY_TOPIC

      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .with(body: hash_including('from' => { 'email' => '<EMAIL>', 'name' => 'DELINQUENCY' }))
        .to_return(status: 200, body: { errors: [] }.to_json)

      described_class.send_message!(personalizations:, sg_template_id:, template:)
    end

    it 'sends an email from the servicing address when the topic of the specified template is servicing' do
      template.detail_info.topic = Template::DetailInfo::SERVICING_TOPIC

      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .with(body: hash_including('from' => { 'email' => '<EMAIL>', 'name' => 'Above Lending' }))
        .to_return(status: 200, body: { errors: [] }.to_json)

      described_class.send_message!(personalizations:, sg_template_id:, template:)
    end

    it 'sends an email from the customer care address when the topic of the specified template is customer care' do
      template.detail_info.topic = Template::DetailInfo::CUSTOMER_CARE_TOPIC

      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .with(body: hash_including('from' => { 'email' => '<EMAIL>', 'name' => 'Above Lending' }))
        .to_return(status: 200, body: { errors: [] }.to_json)

      described_class.send_message!(personalizations:, sg_template_id:, template:)
    end

    it 'sends an email from the customer care address when the topic of the specified template is anything else' do
      template.detail_info.topic = Template::DetailInfo::ORIGINATIONS_TOPIC

      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .with(body: hash_including('from' => { 'email' => '<EMAIL>', 'name' => 'Above Lending' }))
        .to_return(status: 200, body: { errors: [] }.to_json)

      described_class.send_message!(personalizations:, sg_template_id:, template:)
    end

    it 'triggers an email with valid configuration' do
      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .with(
          body: {
            from: anything,
            personalizations: [{
              to: [{
                email: '<EMAIL>'
              }],
              custom_args: { message_id: },
              dynamic_template_data: { name: 'John Doe', amount: '123.45' }
            }],
            template_id: 'my_template_id',
            asm: { group_id: 32_347, groups_to_display: [] }
          }
        )
        .to_return(status: 200, body: { errors: [] }.to_json)

      described_class.send_message!(personalizations:, sg_template_id:, template:)
    end

    it 'enables list bypassing for the message when the override suppression setting is set on the template' do
      template.detail_info.override_suppression = true

      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .with(
          body: hash_including(mail_settings: { bypass_list_management: { enable: true } })
        )
        .to_return(status: 200, body: { errors: [] }.to_json)

      described_class.send_message!(personalizations:, sg_template_id:, template:)
    end

    it 'attaches a VCF card to the email when the attach VCF card setting is set on the template' do
      template.detail_info.attach_vcf_card = true

      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .with(
          body: hash_including(attachments: [{
                                 content: Base64.strict_encode64(Rails.root.join('app/assets/vcf/Above Lending Support.vcf').read),
                                 filename: 'Above Lending Support.vcf',
                                 type: 'text/vcard',
                                 disposition: 'attachment'
                               }])
        )
        .to_return(status: 200, body: { errors: [] }.to_json)

      described_class.send_message!(personalizations:, sg_template_id:, template:)
    end

    it 'does not raise an error when the API request is successful' do
      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .to_return(status: 200, body: { errors: [] }.to_json)

      expect do
        described_class.send_message!(personalizations:, sg_template_id:, template:)
      end.not_to raise_error
    end

    it 'raises a ResponseError when the API request is unsuccessful' do
      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .to_return(status: 400, body: '')

      expect do
        described_class.send_message!(personalizations:, sg_template_id:, template:)
      end.to raise_error(SendgridApi::ResponseError, /Received 400 response./)
    end

    it 'logs the error and re-raises it when an unexpected error occurs' do
      stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
        .to_return(status: 200, body: { errors: ['Invalid email address'] }.to_json)

      allow(SendgridApi).to receive(:parse_response!).and_raise(StandardError.new('Unexpected error'))

      expect(Rails.logger).to receive(:error).with(/Unexpected error/)
      expect do
        described_class.send_message!(personalizations:, sg_template_id:, template:)
      end.to raise_error(StandardError, /Unexpected error/)
    end

    context 'whitelist emails' do
      let(:email) { '<EMAIL>' }

      it 'does not send email on lower environments' do
        expect do
          described_class.send_message!(personalizations:, sg_template_id:, template:)
        end.not_to raise_error
      end

      it 'sends the email on production' do
        allow(Rails.env).to receive(:production?).and_return(true)

        template.detail_info.topic = Template::DetailInfo::DELINQUENCY_TOPIC

        stub_request(:post, 'https://api.sendgrid.com/v3/mail/send')
          .with(body: hash_including('from' => { 'email' => '<EMAIL>', 'name' => 'DELINQUENCY' }))
          .to_return(status: 200, body: { errors: [] }.to_json)

        described_class.send_message!(personalizations:, sg_template_id:, template:)
      end
    end
  end

  describe '.unsubscribed?' do
    let(:email) { '<EMAIL>' }
    let(:body) { {}.to_json }

    it 'returns true when the email is unsubscribed' do
      stub_request(:get, "https://api.sendgrid.com/v3/asm/suppressions/global/#{email}")
        .to_return(status: 200, body: { recipient_email: '<EMAIL>' }.to_json)

      response = SendgridApi.unsubscribed?(email)
      expect(response).to eq(true)
    end

    it 'returns false when the email is not unsubsribed' do
      stub_request(:get, "https://api.sendgrid.com/v3/asm/suppressions/global/#{email}")
        .to_return(status: 200, body:)

      response = SendgridApi.unsubscribed?(email)
      expect(response).to eq(false)
    end

    it 'raises a ResponseError when the API request is unsuccessful' do
      stub_request(:get, "https://api.sendgrid.com/v3/asm/suppressions/global/#{email}")
        .to_return(status: 400, body: { errors: ['Invalid email address'] }.to_json)

      expect do
        SendgridApi.unsubscribed?(email)
      end.to raise_error(SendgridApi::ResponseError, /Received 400 response./)
    end

    context 'when the request results in a rate limit error' do
      it 'raises a RateLimitError' do
        stub_request(:get, "https://api.sendgrid.com/v3/asm/suppressions/global/#{email}")
          .to_return(status: 429, body: { errors: ['Request Ratelimited by Sendgrid'] }.to_json)

        expect do
          SendgridApi.unsubscribed?(email)
        end.to raise_error(SendgridApi::RateLimitError, /Request Ratelimited by Sendgrid./)
      end
    end

    it 'logs the error and re-raises it when an unexpected error occurs' do
      stub_request(:get, "https://api.sendgrid.com/v3/asm/suppressions/global/#{email}")
        .to_return(status: 200, body:)

      allow_any_instance_of(SendGrid::API).to receive(:client).and_raise(StandardError, 'Unexpected error')

      expect(Rails.logger).to receive(:error).with(/Unexpected error/)
      expect do
        SendgridApi.unsubscribed?(email)
      end.to raise_error(StandardError, /Unexpected error/)
    end
  end
end
