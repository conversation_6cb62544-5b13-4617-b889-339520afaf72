# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoggingConcern do
  subject { DummyLoggingConcernTestClass.new }

  it 'logs info with given arguments' do
    expect(Rails.logger).to receive(:info).with('MyMessage', some_key: 'some_value')
    subject.log_info('MyMessage', some_key: 'some_value')
  end

  it 'logs error with given arguments' do
    expect(Rails.logger).to receive(:error).with('MyMessage', some_key: 'some_value')
    subject.log_error('MyMessage', some_key: 'some_value')
  end

  describe '#log_details' do
    it 'logs info with given arguments' do
      expect(Rails.logger).to receive(:info).with('DummyLoggingConcernTestClass - dummy_method: MyMessage', some_key: 'some_value')
      subject.log_details(method_name: 'dummy_method', custom_msg: 'MyMessage', some_key: 'some_value')
    end

    context 'when message is present' do
      let(:message) { create(:message, :email) }
      let(:message_attributes) { subject.send(:extract_message_traits, message) }

      # Message Attributes For Logging
      #  {:delivery_id=>"c513cf86-a6d6-42c4-b88a-eef0e3fb9d4d",
      #   :delivery_type=>"Email",
      #   :detail_info=>
      #    {"delivery_method"=>"EMAIL",
      #     "entities"=>
      #      [{"id"=>"6bd578b2-36c2-4d56-bdc0-8beba3bcad55", "type"=>"BORROWER"}],
      #     "schedule_at"=>nil,
      #     "status"=>"PENDING"},
      #   :loan_id=>SOME_UUID,
      #   :message_id=>"583bdc1c-fe8a-472d-834a-772530142d1d",
      #   :some_key=>"some_value",
      #   :template_id=>"42c075bb-2bc2-450a-a6fc-76af403a5b47",
      #   :template_version_id=>"6a7fec62-5db7-481f-bba5-54c84b7079fa"}]

      it 'logs info with given arguments' do
        expect(Rails.logger).to receive(:info).with('DummyLoggingConcernTestClass - dummy_method: MyMessage', some_key: 'some_value', **message_attributes)
        subject.log_details(method_name: 'dummy_method', custom_msg: 'MyMessage', some_key: 'some_value', message:)
      end
    end
  end
end

class DummyLoggingConcernTestClass
  include LoggingConcern
end
