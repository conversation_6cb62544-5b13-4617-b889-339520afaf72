# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DownloadAttachments do
  let(:file) { { download_url: 'https://example.com/sample.pdf', filename: 'sample.pdf', type: 'application/pdf' } }

  before { allow(Net::HTTP).to receive(:get).and_return('content') }

  describe '#call' do
    it 'downloads a URL content before yield the block' do
      input = { loan_agreement_file: file }
      result = DownloadAttachments.call(input) do |*args|
        expect(args).to eq(['content', 'sample.pdf', 'application/pdf'])
        true
      end

      expect(result).to eq([true])
    end

    it 'works with an array of files' do
      counter = 0
      input = { loan_agreement_file: file, loan_agreement_files: [file, file] }
      result = DownloadAttachments.call(input) { |*| counter += 1 }
      expect(result).to eq([1, 2, 3])
    end

    it 'does nothing when the input has no file input' do
      callback = -> {}
      expect(callback).not_to receive(:call)
      result = DownloadAttachments.call({ phone_number: '15557778888' }, &callback)
      expect(result).to eq([])
    end
  end
end
