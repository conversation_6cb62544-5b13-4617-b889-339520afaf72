# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TextMessageScheduler do
  include ActiveSupport::Testing::TimeHelpers

  let(:template_topic) { Template::DetailInfo::TOPICS.sample }
  let(:template) { create(:template, detail_info: build(:template_detail_info, topic: template_topic)) }
  let(:message) { create(:message, :sms, template:) }

  describe '.call' do
    context 'when the message is for a non-originations template' do
      let(:template_topic) { Template::DetailInfo::TOPICS.excluding(Template::DetailInfo::ORIGINATIONS_TOPIC).sample }

      before { allow(TextMessageScheduler::ServicingScheduler).to receive(:call) }

      it 'uses the ServicingScheduler' do
        TextMessageScheduler.call(message:)
        expect(TextMessageScheduler::ServicingScheduler).to have_received(:call).with(message:)
      end
    end

    context 'when the message is for an originations template' do
      let(:template_topic) { Template::DetailInfo::ORIGINATIONS_TOPIC }

      before { allow(TextMessageScheduler::OriginationsScheduler).to receive(:call) }

      it 'uses the OriginationsScheduler' do
        TextMessageScheduler.call(message:)
        expect(TextMessageScheduler::OriginationsScheduler).to have_received(:call).with(message:)
      end
    end
  end

  describe '.within_schedule?' do
    let(:hour_in_central_time) { (0..24).to_a.sample }

    context 'when the message is for a non-originations template' do
      let(:template_topic) { Template::DetailInfo::TOPICS.excluding(Template::DetailInfo::ORIGINATIONS_TOPIC).sample }

      before { allow(TextMessageScheduler::ServicingScheduler).to receive(:within_schedule?) }

      it 'uses the ServicingScheduler' do
        TextMessageScheduler.within_schedule?(message, hour_in_central_time)
        expect(TextMessageScheduler::ServicingScheduler).to have_received(:within_schedule?).with(hour_in_central_time)
      end
    end

    context 'when the message is for an originations template' do
      let(:template_topic) { Template::DetailInfo::ORIGINATIONS_TOPIC }

      before { allow(TextMessageScheduler::OriginationsScheduler).to receive(:within_schedule?) }

      it 'uses the OriginationsScheduler' do
        TextMessageScheduler.within_schedule?(message, hour_in_central_time)
        expect(TextMessageScheduler::OriginationsScheduler).to have_received(:within_schedule?).with(hour_in_central_time)
      end
    end
  end
end
