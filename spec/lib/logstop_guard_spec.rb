# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LogstopGuard do
  describe '.guard!' do
    it 'delegates to logstop to guard logs' do
      logger = double('Logger')
      expect(Logstop).to receive(:guard).with(logger, anything)

      LogstopGuard.guard!(logger)
    end
  end

  describe '.scrubber' do
    it 'does not break when given a unicode character' do
      scrubbed = LogstopGuard.scrubber.call('↳ account_number: 12324,')
      expect(scrubbed).to include('[FILTERED]')
      expect(scrubbed).not_to include('12324')
    end

    LogstopGuard::SENSITIVE_KEY_SUFFIXES.each do |key_suffix|
      context "#{key_suffix} suffix" do
        it 'filters when unquoted' do
          scrubbed = LogstopGuard.scrubber.call(%(#{key_suffix}: 12324,))
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end

        it 'filters when escaped double quoted' do
          scrubbed = LogstopGuard.scrubber.call(%("#{key_suffix}": 12324,))
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end

        it 'filters when literal double qouted' do
          scrubbed = LogstopGuard.scrubber.call(%("#{key_suffix}": 12324,))
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end

        it 'filters when escaped single quoted' do
          scrubbed = LogstopGuard.scrubber.call(%('#{key_suffix}': 12324,))
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end

        it 'filters when literal single qouted' do
          scrubbed = LogstopGuard.scrubber.call(%('#{key_suffix}': 12324,))
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end

        it 'filters when the value is a symbol key in a Ruby hash' do
          scrubbed = LogstopGuard.scrubber.call({ "#{key_suffix}": '12324' }.to_s)
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end

        it 'filters when the value is a string key in a Ruby hash' do
          scrubbed = LogstopGuard.scrubber.call({ key_suffix.to_s => '12324' }.to_s)
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end

        it 'filters when the value is a string key in a Ruby hash serialized within another Ruby hash' do
          serialized_hash = { key_suffix.to_s => '12324' }.to_s
          scrubbed = LogstopGuard.scrubber.call({ 'wrapper' => serialized_hash }.to_s)
          expect(scrubbed).to include('[FILTERED]')
          expect(scrubbed).not_to include('12324')
        end
      end
    end
  end

  describe '.scrub!' do
    it 'replaces sensitive values in the string with [FILTERED]' do
      message = 'ssn:***********,'
      scrubbed = described_class.scrub!(message)
      expect(scrubbed).to include('[FILTERED]')
      expect(scrubbed).not_to include('***********')
    end

    it 'returns original message if no sensitive keys found' do
      message = 'safe_key:12345'
      scrubbed = described_class.scrub!(message)
      expect(scrubbed).to eq('safe_key:12345')
    end

    it 'returns empty string for a nil input' do
      expect(described_class.scrub!(nil)).to eq('')
    end

    it 'returns empty string for an empty string input' do
      expect(described_class.scrub!('')).to eq('')
    end
  end

  describe '.sanitize_object!' do
    it 'replaces sensitive values in a hash' do
      obj = { ssn: '***********', name: 'Alice' }
      result = described_class.sanitize_object!(obj)

      expect(result[:ssn]).to eq('[FILTERED]')
      expect(result[:name]).to eq('Alice')
    end

    it 'replaces sensitive values in nested hashes' do
      obj = {
        user: {
          email: '<EMAIL>',
          details: { account_number: '**********' }
        }
      }

      result = described_class.sanitize_object!(obj)

      expect(result[:user][:email]).to eq('[FILTERED]')
      expect(result[:user][:details][:account_number]).to eq('[FILTERED]')
    end

    it 'replaces sensitive values in arrays' do
      obj = [
        { ssn: '***********' },
        { name: 'Bob' },
        'recipient: <EMAIL>,'
      ]

      result = described_class.sanitize_object!(obj)

      expect(result[0][:ssn]).to eq('[FILTERED]')
      expect(result[1][:name]).to eq('Bob')
      expect(result[2]).to include('[FILTERED]')
      expect(result[2]).not_to include('<EMAIL>')
    end

    it 'scrubs string values directly' do
      obj = 'authorization: Bearer abc123,'
      result = described_class.sanitize_object!(obj)
      expect(result).to include('[FILTERED]')
      expect(result).not_to include('abc123')
    end

    it 'returns the object as-is for unsupported types' do
      obj = 42
      result = described_class.sanitize_object!(obj)
      expect(result).to eq(42)
    end
  end
end
