# frozen_string_literal: true

# spec/sendgrid_api_spec.rb
require 'rails_helper'

RSpec.describe TemplateVariable, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:key) }
    it { should validate_presence_of(:name) }
    it { should validate_inclusion_of(:type).in_array(TemplateVariable::TYPES) }
    it { should validate_inclusion_of(:subtype).in_array(TemplateVariable::SUBTYPES) }

    context 'default_value validation' do
      it 'is invalid when default_value is nil' do
        template_variable = TemplateVariable.new(default_value: nil)
        template_variable.valid?
        expect(template_variable.errors[:default_value]).to include("can't be nil")
      end

      it 'is valid when default_value is false' do
        template_variable = TemplateVariable.new(default_value: false)
        template_variable.valid?
        expect(template_variable.errors[:default_value]).to be_empty
      end

      it 'is valid when default_value is not nil' do
        template_variable = TemplateVariable.new(type: :string, default_value: 'some value')
        template_variable.valid?
        expect(template_variable.errors[:default_value]).to be_empty
      end
    end
  end

  describe '#valid_for?' do
    context 'array subtype validation' do
      let(:variable) { TemplateVariable.new(type: :date, subtype: :array) }

      it 'errors when it is not array' do
        variable.valid_for?('String')
        expect(variable.error).to eq('must be an array')
      end

      it 'errors when it has invalid dates' do
        variable.valid_for?(['2020-01-01', 'invalid date', '2020-01-02'])
        expect(variable.error).to eq('index[1] must be a date in iso8601 format %Y-%m-%d')
      end
    end
  end
end
