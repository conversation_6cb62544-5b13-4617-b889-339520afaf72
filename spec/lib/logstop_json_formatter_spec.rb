# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LogstopJsonFormatter do
  subject { described_class.new }

  let(:logger) { SemanticLogger::Subscriber.new.logger }

  describe '.call' do
    it 'scrubs the message' do
      log = SemanticLogger::Log.new('TestLog', :info)
      log.message = 'Object called with params {"email"=>"SECRET_VALUE"}'

      result = JSON.parse(subject.call(log, logger))

      expect(result['message']).to eq('Object called with params {"email"=>[FILTERED]}')
    end

    it 'leaves the provided payload unchanged even when content is filtered in the logs' do
      data = { email: 'SECRET_VALUE' }
      log = SemanticLogger::Log.new('TestLog', :info)
      log.payload = data

      result = JSON.parse(subject.call(log, logger))

      expect(result.dig('payload', 'email')).to eq('[FILTERED]')
      expect(data[:email]).to eq('SECRET_VALUE')
    end

    LogstopGuard::SENSITIVE_KEY_SUFFIXES.each do |key_suffix|
      it "filters the payload for any key with the #{key_suffix} suffix" do
        log = SemanticLogger::Log.new('TestLog', :info)
        log.payload = { "abc#{key_suffix}" => 'SECRET_VALUE' }

        result = JSON.parse(subject.call(log, logger))

        expect(result.dig('payload', "abc#{key_suffix}")).to eq('[FILTERED]')
      end
    end

    it 'filters payload values properly for nested keys' do
      log = SemanticLogger::Log.new('TestLog', :info)
      log.payload = { borrower: { ssn: 'SECRET_VALUE' } }

      result = JSON.parse(subject.call(log, logger))

      expect(result.dig('payload', 'borrower', 'ssn')).to eq('[FILTERED]')
    end

    it 'filters payload values properly for data contained in arrays' do
      log = SemanticLogger::Log.new('TestLog', :info)
      log.payload = [{ email: 'SECRET_VALUE' }]

      result = JSON.parse(subject.call(log, logger))

      expect(result.dig('payload', 0, 'email')).to eq('[FILTERED]')
    end
  end
end
