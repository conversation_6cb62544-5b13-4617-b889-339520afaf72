# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SbtOutboundMessageStatusJob, type: :job do
  include ActiveJob::TestHelper

  describe '#perform' do
    let(:message_status_payload) do
      {
        Type: 'MessageStatus',
        Payload: {
          AccountId: SecureRandom.uuid,
          Message: 'test message',
          Msisdn: '1-888-888-8888',
          GroupName: 'Account Group',
          GroupId: SecureRandom.uuid,
          CommunicationCode: '12345',
          DeliveredTime: '2023-06-09T19:58:10.6012211Z',
          Properties: nil,
          StatusCode: '100',
          StatusCodeDescription: 'Delivered',
          MessageId: SecureRandom.uuid,
          ReferenceId: '',
          TotalMessageSegments: 2,
          MessageType: 'Unicast'
        }
      }
    end

    let(:vendor_message_id) { message_status_payload[:Payload][:MessageId] }
    let(:status_code) { message_status_payload[:Payload][:StatusCode] }
    let(:timestamp) { message_status_payload[:Payload][:DeliveredTime] }
    let!(:text_message) { create(:text_message, vendor_message_id:) }
    let!(:message) do
      create(
        :message,
        delivery_id: text_message.id,
        detail_info: build(:message_detail_info, :sms, status: Message::DetailInfo::PENDING_STATUS)
      )
    end

    subject { described_class.perform_sync(vendor_message_id, status_code, timestamp, message_status_payload.to_json) }

    it 'logs the job start' do
      expect(Rails.logger).to receive(:log).with(anything).at_least(:once)

      subject
    end

    it 'creates a TextMessageEvent with the correct attributes' do
      expect { subject }.to change { TextMessageEvent.count }.by(1)

      event = TextMessageEvent.last
      expect(event.text_message_id).to eq(text_message.id)
      expect(event.status_code).to eq(status_code.to_i)
      expect(event.payload).to eq(message_status_payload.as_json)
    end

    it 'updates the status of the associated Message when the status code from SBT matches delivered' do
      expected_event = {
        name: described_class.name,
        meta: { message_id: message.id },
        success: true
      }
      expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

      expect { subject }.to change { message.reload.detail_info.status }.from(Message::DetailInfo::PENDING_STATUS).to(Message::DetailInfo::DELIVERED_STATUS)
    end

    it 'updates the status of the associated Message when the status code from SBT matches triggered' do
      expected_event = {
        name: described_class.name,
        meta: { message_id: message.id },
        success: true
      }
      expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

      message_status_payload[:Payload][:StatusCode] = '101'
      expect { subject }.to change { message.reload.detail_info.status }.from(Message::DetailInfo::PENDING_STATUS).to(Message::DetailInfo::TRIGGERED_STATUS)
    end

    it 'updates the status of the associated Message when the status code from SBT matches failed' do
      expected_event = {
        name: described_class.name,
        meta: { message_id: message.id },
        reason: 'FAILED',
        success: false
      }
      expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

      message_status_payload[:Payload][:StatusCode] = '200'
      expect { subject }.to change { message.reload.detail_info.status }.from(Message::DetailInfo::PENDING_STATUS).to(Message::DetailInfo::FAILED_STATUS)
    end

    it 'does not update the message status when processing an earlier event' do
      create(:text_message_event, created_at: DateTime.parse(timestamp) + 1.second)

      message.detail_info.status = Message::DetailInfo::DELIVERED_STATUS
      message.save!

      described_class.perform_sync(vendor_message_id, status_code, timestamp, message_status_payload.to_json)
      expect(message.reload.detail_info.status).to eq(Message::DetailInfo::DELIVERED_STATUS)
    end

    context 'when receiving an un-recognized message ID from SBT' do
      before do
        text_message.destroy!
      end

      it 'records a success event with the ignored flag present' do
        expected_event = {
          name: described_class.name,
          meta: { vendor_message_id:, ignored: true },
          success: true
        }
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        subject
      end

      it 'does NOT create a TextMessageEvent' do
        expect { subject }.not_to(change { TextMessageEvent.count })
      end
    end

    context 'when receiving an un-recognized status code from SBT' do
      it 'creates a TextMessageEvent' do
        expect { subject }.to change { TextMessageEvent.count }.by(1)
      end

      it 'changes the status of the Message to unknown' do
        expected_event = {
          name: described_class.name,
          meta: { message_id: message.id },
          reason: 'UNKNOWN',
          success: false
        }
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        expect do
          described_class.perform_sync(vendor_message_id, 1000, timestamp, message_status_payload.to_json)
        end.to change { message.reload.detail_info.status }.from(Message::DetailInfo::PENDING_STATUS).to(Message::DetailInfo::UNKNOWN_STATUS)
      end
    end
  end
end
