# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SendgridEmailDeliveryEventJob, type: :job do
  include ActiveJob::TestHelper

  let(:message) { create(:message, :email) }
  let(:message_id) { message.id }
  let(:event_type) { 'delivered' }
  let(:timestamp) { 1_513_299_569 }

  let(:sendgrid_params) do
    {
      attempt: '1',
      category: 'cat facts',
      email: '<EMAIL>',
      event: event_type,
      sg_event_id: SecureRandom.uuid,
      sg_message_id: SecureRandom.uuid,
      'smtp-id': SecureRandom.uuid,
      response: '250 OK',
      timestamp:,
      message_id:
    }
  end

  describe '#perform' do
    it 'logs an info message when the job is triggered' do
      expect(Rails.logger).to receive(:log).with(anything).at_least(:once)

      described_class.perform_sync(event_type, message_id, timestamp, sendgrid_params.to_json)
    end

    it 'creates a new EmailEvent with the correct attributes' do
      expect { described_class.perform_sync(event_type, message_id, timestamp, sendgrid_params.to_json) }.to change { EmailEvent.count }.by(1)

      last_email_event = EmailEvent.last
      expect(last_email_event.event).to eq(event_type)
      expect(last_email_event.payload).to eq(sendgrid_params.as_json)
      expect(last_email_event.email).to eq(message.delivery)
    end

    it 'updates the status of the message when message status is supported' do
      expected_event = {
        name: described_class.name,
        meta: { message_id: },
        success: true
      }
      expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

      described_class.perform_sync(event_type, message_id, timestamp, sendgrid_params.to_json)

      expect(message.reload.detail_info.status).to eq(Message::DetailInfo::DELIVERED_STATUS)
    end

    it 'updates the status of a message when message status is unsupported' do
      unknown_event_type = 'unknown'
      unknown_event_params = sendgrid_params.merge(event: unknown_event_type)

      expected_event = {
        name: described_class.name,
        meta: { message_id: },
        reason: 'UNKNOWN',
        success: false
      }
      expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

      expect { described_class.perform_sync(unknown_event_type, message_id, timestamp, unknown_event_params.to_json) }.to change { message.reload.detail_info.status }.to(Message::DetailInfo::UNKNOWN_STATUS)
    end

    context 'when receiving an un-recognized message ID from Sendgrid' do
      before do
        message.destroy!
      end

      it 'records a success event with the ignored flag present' do
        expected_event = {
          name: described_class.name,
          meta: { message_id:, ignored: true },
          success: true
        }
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        described_class.perform_sync(event_type, message_id, timestamp, sendgrid_params.to_json)
      end

      it 'does NOT create a EmailEvent' do
        expect { described_class.perform_sync(event_type, message_id, timestamp, sendgrid_params.to_json) }.not_to(change { EmailEvent.count })
      end
    end

    context 'when receiving an engagement event' do
      let(:event_type) { 'open' }

      it 'creates an EmailEvent' do
        expect { described_class.perform_sync(event_type, message_id, timestamp, sendgrid_params.to_json) }.to change { EmailEvent.count }.by(1)

        last_email_event = EmailEvent.last
        expect(last_email_event.event).to eq(event_type)
        expect(last_email_event.payload).to eq(sendgrid_params.as_json)
      end

      it 'does not update the message status' do
        message.detail_info.status = Message::DetailInfo::DELIVERED_STATUS
        message.save!

        described_class.perform_sync('click', message_id, timestamp, sendgrid_params.to_json)
        expect(message.reload.detail_info.status).to eq(Message::DetailInfo::DELIVERED_STATUS)
      end
    end

    sendgrid_events = %w[processed delivered deferred dropped bounce]

    unpermitted_updates = {
      Message::DetailInfo::UNDELIVERABLE_STATUS => %w[processed],
      Message::DetailInfo::UNKNOWN_STATUS => %w[processed deferred]
    }

    [
      Message::DetailInfo::UNSCHEDULABLE_STATUS,
      Message::DetailInfo::PENDING_STATUS,
      Message::DetailInfo::TRIGGERED_STATUS,
      Message::DetailInfo::UNDELIVERABLE_STATUS,
      Message::DetailInfo::UNKNOWN_STATUS
    ].each do |current_status|
      context "when the message is currently in the #{current_status} status" do
        before do
          message.detail_info.status = current_status
          message.save!
        end

        sendgrid_events.each do |event|
          context "when receiving an #{event} event" do
            let(:event_type) { event }

            if unpermitted_updates[current_status]&.include?(event)
              it 'does NOT update the message status' do
                described_class.perform_sync(event, message_id, timestamp, sendgrid_params.to_json)
                expect(message.reload.detail_info.status).to eq(current_status)
              end
            else
              it 'updates the message status' do
                described_class.perform_sync(event, message_id, timestamp, sendgrid_params.to_json)
                expect(message.reload.detail_info.status).to eq(SendgridEmailDeliveryEventJob::SENDGRID_DELIVERY_EVENT_TO_STATUS[event])
              end
            end
          end
        end
      end
    end

    [Message::DetailInfo::DELIVERED_STATUS, Message::DetailInfo::UNSUBSCRIBED_STATUS, Message::DetailInfo::FAILED_STATUS].each do |terminal_status|
      context "when the message is currently in the #{terminal_status} status" do
        before do
          message.detail_info.status = terminal_status
          message.save!
        end

        sendgrid_events.each do |event|
          context "when receiving an #{event} event" do
            it 'does NOT update the message status' do
              described_class.perform_sync(event, message_id, timestamp, sendgrid_params.to_json)
              expect(message.reload.detail_info.status).to eq(terminal_status)
            end
          end
        end
      end
    end
  end
end
