# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SendEmailJob, type: :job do
  include ActiveJob::TestHelper
  include ActiveSupport::Testing::TimeHelpers

  let(:message) { create(:message, :email) }

  it { is_expected.to be_retryable 5 }

  describe '#perform' do
    subject { described_class.new.perform(message.id) }

    it 'sends the email' do
      recipient = message.detail_info.recipient
      sg_template_id = message.template_version.detail_info.vendor_id
      template_data = message.detail_info.inputs
      template = message.template_version.template
      personalization = double('SendGrid::Personalization')

      expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
      expect(SendgridApi).to receive(:build_personalization).with(
        email: recipient,
        template_data: AttributeFormatter.call(template_data),
        message_id: message.id
      ).and_return(personalization)

      expect(SendgridApi).to receive(:send_message!).with(
        attachments: [],
        personalizations: [personalization],
        sg_template_id:,
        template:
      )
      expected_event = {
        name: described_class.name,
        meta: { message_id: message.id },
        success: true
      }
      expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

      subject

      expect(message.reload.delivery_type).to eq('Email')
      expect(message.reload.detail_info.status).to eq(Message::DetailInfo::TRIGGERED_STATUS)
    end

    it 'formats attributes before passing to sendgrid api personalization' do
      message.detail_info.inputs = { first_payment_on: '2024-07-12', payment_amount: 100.1 }
      message.save!

      expected_attributes = {
        'CUSTOMER_SERVICE_PHONE_NUMBER' => '************',
        'TEXT_MESSAGE_OPT_OUT' => 'Reply STOP to opt out.',
        'first_payment_on' => '07/12/2024',
        'payment_amount' => '100.10'
      }

      expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
      expect(SendgridApi).to receive(:build_personalization).with(
        hash_including(template_data: expected_attributes)
      )
      expect(SendgridApi).to receive(:send_message!)

      subject
    end

    it 'properly builds attachments before sending a message' do
      sample_file = { download_url: 'https://example.com/sample.pdf', filename: 'sample.pdf', type: 'application/pdf' }
      allow(DownloadAttachments).to receive(:call).and_yield('content', *sample_file.values_at(:filename, :type))
      message.detail_info.inputs = { loan_agreement_file: sample_file }

      expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
      expect(SendgridApi).to receive(:build_attachment).once.with(hash_including(content: 'content'))
      expect(SendgridApi).to receive(:send_message!)

      subject
    end
  end
end
