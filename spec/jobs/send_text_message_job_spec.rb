# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SendTextMessageJob, type: :job do
  include ActiveJob::TestHelper
  include ActiveSupport::Testing::TimeHelpers

  let(:template) { build(:template, detail_info: build(:template_detail_info, key: 'payment_past_due_5_days')) }
  let(:template_key) { template.detail_info.key }
  let(:content) do
    <<-MSG.squish
      {{first_name}}, your Above Lending loan payment of ${{payment_amount}} is past due.
      Please call us at {{CUSTOMER_SERVICE_PHONE_NUMBER}} or visit abovelending.com/signin to make your payment.
      {{TEXT_MESSAGE_OPT_OUT}}'}
    MSG
  end
  let(:text_message_body) do
    <<-MSG.squish
      Test, your Above Lending loan payment of $150.45 is past due.
      Please call us at #{MessageConstants::CUSTOMER_SERVICE_PHONE_NUMBER} or visit abovelending.com/signin to make your payment.
      #{MessageConstants::TEXT_MESSAGE_OPT_OUT}'}
    MSG
  end
  let(:template_version) do
    build(
      :template_version,
      detail_info: build(
        :template_version_detail_info,
        variables: %w[first_name payment_amount],
        content:
      )
    )
  end
  let(:detail_info) { build(:message_detail_info, :sms, status: 'PENDING', inputs: { 'first_name' => 'Test', 'payment_amount' => 150.45 }) }
  let(:message) { create(:message, detail_info:, template:, template_version:) }
  let(:phone_number) { message.detail_info.recipient }
  let(:vendor_message_id) { SecureRandom.uuid }

  describe '#perform' do
    subject { described_class.new.perform(message.id) }

    before do
      travel_to(InCentralTime.at_noon)
    end

    context 'when job executes outside of allowed hours' do
      it 'logs and exits when executing before hours' do
        travel_to(InCentralTime.now.change(hour: 6))
        expect(SbtApi).not_to receive(:send_message!)

        expected_event = {
          name: described_class.name,
          meta: { message_id: message.id },
          reason: 'unschedulable',
          success: false
        }
        expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        subject

        expect(message.reload.detail_info.status).to eq(Message::DetailInfo::UNSCHEDULABLE_STATUS)
      end

      it 'logs and exits when executing after hours' do
        travel_to(InCentralTime.now.change(hour: 20))
        expect(SbtApi).not_to receive(:send_message!)

        expected_event = {
          name: described_class.name,
          meta: { message_id: message.id },
          reason: 'unschedulable',
          success: false
        }
        expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        subject

        expect(message.reload.detail_info.status).to eq(Message::DetailInfo::UNSCHEDULABLE_STATUS)
      end
    end

    context 'when the subscriber has opted out' do
      it 'does not send an SMS message to the phone number provided' do
        allow(SbtApi).to receive(:get_status).and_return('data' => [{ 'status' => 'InActive' }])
        expect(SbtApi).not_to receive(:valid_phone_number?)
        expect(SbtApi).not_to receive(:add_subscriber!)
        expect(SbtApi).not_to receive(:send_message!)
        expected_event = {
          name: described_class.name,
          meta: { message_id: message.id },
          reason: 'unsubscribed',
          success: false
        }
        expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        subject
      end
    end

    context 'when a subscriber uses a landline number' do
      it 'changes the message status to UNDELIVERABLE_STATUS and logs a warning' do
        allow(SbtApi).to receive(:get_status).and_return('data' => [{ 'status' => 'Not a subscriber' }])
        allow(SbtApi).to receive(:valid_phone_number?).with(phone_number).and_return(false)

        expected_event = {
          name: described_class.name,
          meta: { message_id: message.id },
          reason: 'landline',
          success: false
        }
        expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        expect { subject }.to change { message.reload.detail_info.status }.from('PENDING').to(Message::DetailInfo::UNDELIVERABLE_STATUS)
      end
    end

    context 'when sending an SMS message to new subscriber' do
      it 'validates the phone number provided and creates a new subscriber' do
        allow(SbtApi).to receive(:get_status).and_return('data' => ['status' => 'Not a subscriber'])
        allow(SbtApi).to receive(:valid_phone_number?).with(phone_number).and_return(true)
        expect(SbtApi).to receive(:add_subscriber!).with(phone_number)
        expect(SbtApi).to receive(:send_message!).with(phone_number, text_message_body)

        expected_event = {
          name: described_class.name,
          meta: { message_id: message.id },
          success: true
        }
        expect(Rails.logger).to receive(:log).with(anything).at_least(:once)
        expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('AsyncEvent', expected_event)

        subject
      end
    end

    context 'when sending an SMS message to an Active subscriber' do
      it 'does not validate or re-subscribe the phone number provided' do
        allow(SbtApi).to receive(:get_status).and_return('data' => [{ 'status' => 'Active' }])
        expect(SbtApi).not_to receive(:valid_phone_number?)
        expect(SbtApi).not_to receive(:add_subscriber!)
        expect(SbtApi).to receive(:send_message!).with(phone_number, text_message_body)

        subject
      end
    end

    context 'when phone_number is not whitelisted' do
      let(:detail_info) { build(:message_detail_info, :sms, recipient: '**************', status: 'PENDING', inputs: { 'first_name' => 'Test', 'payment_amount' => 150.45 }) }
      it 'does not send_message in lower enviornments' do
        allow(Rails.logger).to receive(:warn)
        expect(Rails.logger).to receive(:warn).with("Phone number #{phone_number} is not whitelisted for message_id: #{message.id}")
        expect(SbtApi).not_to receive(:get_status)
        expect(SbtApi).not_to receive(:send_message!).with(phone_number, text_message_body)

        subject
      end

      it 'sends message in production enviornment' do
        allow(Rails.env).to receive(:production?).and_return(true)
        allow(SbtApi).to receive(:get_status).and_return('data' => [{ 'status' => 'Active' }])

        expect(SbtApi).to receive(:get_status)
        expect(SbtApi).to receive(:send_message!).with(phone_number, text_message_body)

        subject
      end
    end
  end
end
