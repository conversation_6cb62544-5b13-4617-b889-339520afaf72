# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailCampaignJob::Coordinator, type: :job do
  include ActiveJob::TestHelper

  describe '#perform' do
    let(:campaign_name) { 'test_campaign' }
    let(:template_name) { 'annual_privacy_notice' }
    let(:delivery_rate) { (1..email_count).to_a.sample }

    let(:test_path) { Rails.root.join('spec', 'support', 'files', 'email_campaign_mock.csv') }
    let(:parsed_test_data) { CSV.parse(File.read(test_path), headers: true) }
    let(:email_count) { parsed_test_data.count }

    let!(:s3_client) { Aws::S3::Client.new(stub_responses: { get_object: { body: File.open(test_path) } }) }
    let!(:template) { create(:template, detail_info: build(:template_detail_info, key: template_name)) }
    let!(:template_version) { create(:template_version, template:) }

    before do
      allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
      assert_no_enqueued_jobs
    end

    let(:call) { described_class.perform_sync(campaign_name, template_name, delivery_rate) }
    let(:expected_workers_count) { (email_count.to_f / delivery_rate).ceil }

    it 'enqueues the correct number of Worker Jobs' do
      call

      expect(EmailCampaignJob::Worker).to have_enqueued_sidekiq_job.exactly(expected_workers_count).times
    end

    it 'sets the correct parameters and wait times for each Worker' do
      call

      expected_workers_count.times do |index|
        expect(EmailCampaignJob::Worker).to have_enqueued_sidekiq_job
          .with(index, campaign_name, template_name, delivery_rate)
          .in((5 + index).minutes)
      end
    end

    context 'when delivery rate is greater than email count' do
      let(:delivery_rate) { email_count + 1 }

      it 'enqueues exactly one Worker Job' do
        call

        expect(EmailCampaignJob::Worker).to have_enqueued_sidekiq_job.exactly(1).times
      end
    end

    context 'with default delivery rate' do
      it 'uses the DEFAULT_DELIVERY_RATE constant' do
        stub_const('EmailCampaignJob::DEFAULT_DELIVERY_RATE', 5)
        described_class.perform_sync(campaign_name, template_name)

        expect(EmailCampaignJob::Worker).to have_enqueued_sidekiq_job.exactly(email_count / 5).times
      end
    end
  end
end
