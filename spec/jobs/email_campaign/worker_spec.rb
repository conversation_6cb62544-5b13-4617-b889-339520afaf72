# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailCampaignJob::Worker, type: :job do
  let(:campaign_name) { 'test_campaign' }
  let(:template_name) { 'annual_privacy_notice' }
  let(:worker_index) { 2 } # test data has 20 rows, this will start processing from middle i.e. 10th row
  let(:delivery_rate) { 5 }

  let(:test_path) { Rails.root.join('spec', 'support', 'files', 'email_campaign_mock.csv') }
  let(:parsed_test_data) { CSV.parse(File.read(test_path), headers: true) }
  let!(:s3_client) { Aws::S3::Client.new(stub_responses: { get_object: { body: File.open(test_path) } }) }
  let!(:template) { create(:template, detail_info: build(:template_detail_info, key: template_name)) }
  let!(:template_version) { create(:template_version, template:) }

  before { allow(Aws::S3::Client).to receive(:new).and_return(s3_client) }

  let(:call) { subject.perform(worker_index, campaign_name, template_name, delivery_rate) }

  describe '#perform' do
    describe 'validations' do
      context 'when contacts file is empty' do
        let!(:s3_client) { Aws::S3::Client.new(stub_responses: { get_object: { body: '' } }) }

        it 'logs an error' do
          expect(Rails.logger).to receive(:error)
            .with("Error during processing of worker #{worker_index} with campaign_name: #{campaign_name}" \
                  ", template_name: #{template_name}, error: No contact emails found for campaign_name: #{campaign_name}")
          call
        end
      end

      context 'when template is not found' do
        let!(:template) { create(:template, detail_info: build(:template_detail_info, key: 'wrong_template_name')) }

        it 'logs an error' do
          expect(Rails.logger).to receive(:error)
            .with("Error during processing of worker #{worker_index} with campaign_name: #{campaign_name}" \
                  ", template_name: #{template_name}, error: Template with name #{template_name} not found")
          call
        end
      end
    end

    describe 'creating message and email delivery records' do
      it 'creates message and email delivery records as specified by delivery rate' do
        expect { call }
          .to change(Message, :count).by(delivery_rate)
          .and change(Email, :count).by(delivery_rate)
      end

      it 'creates message records with correct emails' do
        call

        recipient_emails = parsed_test_data.drop(worker_index * delivery_rate).take(delivery_rate).map { |row| row['EMAIL'] }
        expect(Message.all.pluck(:detail_info).map(&:recipient)).to match_array(recipient_emails)
      end

      it 'creates message records with correct template and template version' do
        call

        Message.all.each do |message|
          expect(message.template).to eq(template)
          expect(message.template_version).to eq(template_version)
        end
      end

      it 'creates message records with correct detail info' do
        call

        Message.all.each do |message|
          expect(message.detail_info.delivery_method).to eq('EMAIL')
          expect(message.detail_info.inputs).to be_empty
          expect(message.detail_info.status).to eq('TRIGGERED')
        end
      end

      it 'assigns the appropriate attribution entities to each message' do
        call

        processed_rows = parsed_test_data.drop(worker_index * delivery_rate).take(delivery_rate)
        processed_rows.each do |row|
          message = Message.find_by('detail_info->>\'recipient\' = ?', row['EMAIL'])

          detail_info_entities = message.detail_info.entities
          expect(detail_info_entities.count).to eq(2)
          expect(detail_info_entities.first.id).to eq(campaign_name)
          expect(detail_info_entities.first.type).to eq('APPLICATION')

          expect(detail_info_entities.second.id).to eq(row['LOAN_ID'])
          expect(detail_info_entities.second.type).to eq('LOAN')
        end
      end

      it 'creates email delivery records associated with message' do
        call

        Message.all.each do |message|
          delivery_id = message.delivery_id
          email_delivery_record = Email.find(delivery_id)
          expect(email_delivery_record).to be_present
        end
      end
    end

    it 'enqueues SendEmail job with right parameters' do
      call

      expect(EmailCampaignJob::SendEmail).to(
        have_enqueued_sidekiq_job.with do |message_payload, job_attr_template_name|
          expect(job_attr_template_name).to eq template_name

          expect(message_payload.count).to eq delivery_rate
          message_payload.each do |attribute|
            message = Message.find(attribute['id'])
            expect(message).to be_present
            expect(message.detail_info.recipient).to eq attribute['recipient']
          end
        end
      )
    end

    context 'when an error occurs during sending of email' do
      before do
        allow(EmailCampaignJob::SendEmail).to receive(:perform_async).and_raise(RuntimeError, 'Error enqueuing email job')
      end

      it 'does not raise error' do
        expect { call }.not_to raise_error
      end

      it 'does not create message and email delivery records' do
        expect { call }
          .to change(Message, :count).by(0)
          .and change(Email, :count).by(0)
      end
    end
  end
end
