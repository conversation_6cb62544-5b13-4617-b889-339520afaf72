# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailCampaignJob::SendEmail, type: :job do
  let!(:template) { create(:template, detail_info: build(:template_detail_info, key: template_name)) }
  let!(:template_version) { create(:template_version, template:) }
  let!(:messages) { create_list(:message, 3, detail_info: build(:message_detail_info, :email, recipient: Faker::Internet.email)) }

  let(:template_name) { 'annual_privacy_notice' }
  let(:message_data) { messages.map { |message| { 'id' => message.id, 'recipient' => message.detail_info.recipient } } }
  let(:call) { subject.perform(message_data, template_name) }

  before { allow(SendgridApi).to receive(:send_message!) }

  it 'sends calls Sendgrid API with right parameters' do
    call

    expect(SendgridApi).to have_received(:send_message!) do |personalizations:, sg_template_id:, template:|
      expect(sg_template_id).to eq(template.active_version.detail_info.vendor_id)
      expect(template).to eq(template)

      expect(personalizations.count).to eq(messages.count)
      personalizations.each do |personalization|
        message = Message.find(personalization.custom_args['message_id'])
        expect(message).to be_present
        expect(personalization.tos.first['email']).to eq(message.detail_info.recipient)
      end
    end
  end
end
