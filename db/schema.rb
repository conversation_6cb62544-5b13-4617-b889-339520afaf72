# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_06_25_225950) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pgcrypto"
  enable_extension "plpgsql"
  enable_extension "uuid-ossp"

  create_table "email_events", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "email_id", null: false
    t.string "event", null: false
    t.jsonb "payload", null: false
    t.timestamptz "created_at", null: false
    t.index ["email_id"], name: "index_email_events_on_email_id"
  end

  create_table "emails", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.timestamptz "created_at", null: false
  end

  create_table "messages", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "delivery_type"
    t.uuid "delivery_id"
    t.jsonb "detail_info"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at", null: false
    t.uuid "template_id", null: false
    t.integer "lock_version"
    t.uuid "template_version_id"
    t.index ["created_at"], name: "index_messages_on_created_at"
    t.index ["template_version_id"], name: "index_messages_on_template_version_id"
  end

  create_table "template_versions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "template_id"
    t.jsonb "detail_info"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at", null: false
    t.index ["template_id"], name: "index_template_versions_on_template_id"
  end

  create_table "templates", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.jsonb "detail_info"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at", null: false
    t.integer "lock_version"
  end

  create_table "text_message_events", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "text_message_id", null: false
    t.integer "status_code", null: false
    t.jsonb "payload", null: false
    t.timestamptz "created_at", null: false
  end

  create_table "text_messages", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "vendor_message_id", null: false
    t.timestamptz "created_at", null: false
  end

  create_table "versions", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object"
    t.timestamptz "created_at", null: false
  end

  add_foreign_key "email_events", "emails"
  add_foreign_key "messages", "template_versions"
  add_foreign_key "messages", "templates"
  add_foreign_key "template_versions", "templates"
end
