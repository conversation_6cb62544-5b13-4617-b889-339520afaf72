class RemoveUnusedIndexes < ActiveRecord::Migration[7.2]
  disable_ddl_transaction!

  # Note Adding `if_exists` because pg_extras does not appear to have the same unused indexes callout
  def up
    remove_index :versions, name: "index_versions_on_item_type_and_item_id", algorithm: :concurrently, if_exists: true
    # Not Used on production, so removing for all environments.
    remove_index :messages, name: "index_messages_on_template_id", algorithm: :concurrently, if_exists: true
  end

  def down
    add_index :versions, %i[item_type item_id], algorithm: :concurrently
    add_index :messages, :template_id, algorithm: :concurrently
  end
end
