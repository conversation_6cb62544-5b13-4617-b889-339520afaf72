class AddTemplateVersionIdToMessages < ActiveRecord::Migration[7.0]
  def up
    add_column :messages, :template_version_id, :uuid

    backfill_template_versions = <<-SQL
      WITH missing_template_versions AS (
        SELECT templates.id as template_id
        FROM templates
        LEFT JOIN template_versions
          ON templates.id = template_versions.template_id
        WHERE template_versions.id IS NULL
      )

      INSERT INTO template_versions (template_id, detail_info, created_at, updated_at)
        SELECT template_id, '{ "status": "ACTIVE" }'::jsonb as detail_info, NOW(), NOW() FROM missing_template_versions
    S<PERSON>

    execute backfill_template_versions

    backfill_template_verison_association = <<-SQL
      WITH active_template_versions AS (
        SELECT templates.id AS template_id, template_versions.id AS template_version_id
        FROM templates, template_versions
        WHERE templates.id = template_versions.template_id AND template_versions.detail_info->>'status' = 'ACTIVE'
      )

      UPDATE messages
      SET template_version_id = active_template_versions.template_version_id
      FROM active_template_versions
      WHERE messages.template_id = active_template_versions.template_version_id
    SQL

    execute backfill_template_verison_association

    change_column_null :messages, :template_version_id, :false
  end

  def down
    remove_column :messages, :template_version_id, :uuid
  end
end
