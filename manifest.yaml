---

# This YAML file serves as a living declaration of Environment Variables for this Application.
# It provides a common interface between Application Developers and DevOps.
# https://www.notion.so/Environment-Variables-bcbb3a0c9e1d40b1bc3f19e2d4337c8c
# https://abovelending.atlassian.net/wiki/spaces/PROD/pages/1642692650/Environment+Variables

global:
  - name: COMMUNICATIONS_SERVICE
    path: "/global/COMMUNICATIONS_SERVICE_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: SIDEKIQ_DASHBOARD_USERNAME
      description: Username for Sidekiq Dashboard
      type: string
    - name: BUNDLE_GEMS__CONTRIBSYS__COM
      description: Secret for bundle to install sidekiq-pro
      type: string
    - name: FLIPPER_UI_SECRET
      description: Secret for flipper ui
      type: string
    - name: IDENTITY_SERVICE_AUTH_HEADER
      description: HTTP Header used by the Identity Service to provide a token representing that the request has been authorized
    - name: FLIPPER_SLACK_HOOK
      description: Slack webhook for flipper notifications
      type: string
environmental:
  - name: COMMUNICATIONS_SERVICE
    path: "/$ENV/COMMUNICATIONS_SERVICE_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: CLUSTERED_REDIS_URI
      description: |
        The Redis connection URI for a clustered Redis instance, which is intended to support this service's
        application caching needs. This should always be configured alongside a CLUSTERED_REDIS_PREFIX environment
        variable that defines the prefix within this cluster allocated specifically to this application.
      type: string
    - name: CLUSTERED_REDIS_PREFIX
      description: The namespace within the clustered Redis instance that is allocated to this application.
      type: string
    - name: DATABASE_USERNAME
      description: Aurora DB username
      type: string
    - name: DATABASE_PASSWORD
      description: Aurora DB password
      type: string
    - name: DEDICATED_REDIS_URI
      description: |
        The Redis connection URI for a dedicated Redis instance, which is intended to serve as the data store for this
        service's Sidekiq server and workers.
      type: string
    - name: INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_FROM_HOUR
      description: This is the beginning of the scheduling window for sending informative email
      type: string
    - name: INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_UNTIL_HOUR
      description: This is the end of the scheduling window for sending informative email
      type: string
    - name: JWT_PUBLIC_KEY_PATH
      description: Path to the RSA Public Key file to be used when verifying JWT generated by this application.
      type: string
    - name: LOG_LEVEL
      description: The minimum level to log. One of DEBUG, INFO, WARN, ERROR, FATAL.
      type: string
    - name: RAILS_ENV
      description: Rails Environment
      type: string
    - name: SECRET_KEY_BASE
      description: Rails Encryption Key
      type: string
    - name: SIDEKIQ_DASHBOARD_PASSWORD
      description: Sidekiq Dashboard Password
      type: string
    - name: SOLUTIONS_BY_TEXT_CLIENT_ID
      description: OAuth2 Client ID used to authenticate with the Solutions By Text API
      type: string
    - name: SOLUTIONS_BY_TEXT_CLIENT_SECRET
      description: OAuth2 Client Secret used to authenticate with the Solutions By Text API
      type: string
    - name: SOLUTIONS_BY_TEXT_BASE_URL
      description: The base url used to executed Solutions By Text API requests
      type: string
    - name: SOLUTIONS_BY_TEXT_AUTH_URL
      description: The authentication url used to execute the OAuth2 client credentials grant
      type: string
    - name: SOLUTIONS_BY_TEXT_GROUP_ID
      description: The global Solutions By Text group that subscribed phone numbers belong to
      type: string
    - name: SENDGRID_API_KEY
      description: This is the unique key used to authenticate with the Sendgrid API
      type: string
    - name: ORIGINATIONS_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR
      description: This is the beginning of the scheduling window for sending originations related text messages
      type: string
    - name: ORIGINATIONS_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR
      description: This is the end of the scheduling window for sending originations related text messages
      type: string
    - name: SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR
      description: This is the beginning of the scheduling window for sending servicing related text messages
      type: string
    - name: SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR
      description: This is the end of the scheduling window for sending servicing related text messages
      type: string
vendor:
  - name: AURORA
    path: '/$ENV/AURORA_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: AURORA_HOST
        description: Aurora database host
        type: string
      - name: AURORA_PORT
        description: Aurora database port
        type: string
