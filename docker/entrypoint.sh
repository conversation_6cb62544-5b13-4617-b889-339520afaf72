#!/bin/sh
set -e

COMMAND=$1 && shift 1

case "$COMMAND" in

  'run-migrations' )
    echo "Migrating database"
    bundle exec rails db:migrate
    echo "Seeding database"
    bundle exec rails db:seed
  ;;

  'start-web' )
    echo "Starting Server..."
    bundle exec rails s -p 3002 -b '0.0.0.0'
  ;;

  'start-alp-development' )
    echo "Migrating database"
    bundle exec rails db:migrate
    echo "Seeding database"
    bundle exec rails db:seed
    echo "Starting Server..."
    bundle exec rails s -p 3002 -b '0.0.0.0'
  ;;

  # This option is only meant for local development
  # The kubernetes deployment will directly call sidekiq instead of the entrypoint script
  # Decision made based on sidekiq docs, to gracefully handle the TERM signal
  # https://github.com/sidekiq/sidekiq/wiki/Kubernetes#safe-shutdown
  'start-sidekiq' )
    echo "Starting Sidekiq in $RAILS_ENV"
    bundle exec sidekiq -e "$RAILS_ENV" -C config/sidekiq.yml
  ;;

  'command' )
    echo "Running command $@"
    exec "$@"
  ;;

  * )
    echo "Unknown command: $COMMAND "
    ;;
esac

exec "$@"
