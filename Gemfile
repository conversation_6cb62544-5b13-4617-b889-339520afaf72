# frozen_string_literal: true

source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.3.7'

gem 'rails', '~> 7.2.0'

gem 'pg'
gem 'pghero'
gem 'puma'
gem 'rails-pg-extras'

gem 'redis'
gem 'redis-clustering'
gem 'redis-namespace'

gem 'bootsnap', require: false
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]

gem 'git'

gem 'active_model_serializers'
gem 'aws-sdk-s3'
gem 'datadog', require: 'datadog/auto_instrument'
gem 'dogstatsd-ruby'
gem 'faraday'
gem 'flipper'
gem 'flipper-notifications'
gem 'flipper-redis'
gem 'flipper-ui'
gem 'jwt'
gem 'liquid'
gem 'logstop'
gem 'pagy'
gem 'paper_trail'
gem 'rails_semantic_logger'
gem 'sendgrid-ruby'
gem 'utf8-cleaner' # can be removed once the this rails issue is resolved: https://github.com/rails/rails/issues/52114

gem 'sidekiq'
source 'https://gems.contribsys.com/' do
  gem 'sidekiq-pro'
end

gem 'rswag-api'
gem 'rswag-ui'

gem 'importmap-rails'
gem 'sprockets-rails'
gem 'stimulus-rails'
gem 'store_model'
gem 'turbo-rails'

group :development, :test do
  gem 'brakeman'
  gem 'dotenv-rails'
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'guard'
  gem 'guard-rspec'
  gem 'pry'
  gem 'rails-erd'
  gem 'rswag-specs'
  gem 'rubocop'
  gem 'shoulda-matchers'

  gem 'debug', platforms: %i[mri mingw x64_mingw]
end

group :development do
  gem 'pry-remote'
end

group :test do
  gem 'axe-matchers'
  gem 'rails-controller-testing'
  gem 'rspec_junit_formatter'
  gem 'rspec-rails'
  gem 'rspec-sidekiq'
  gem 'simplecov', require: false

  gem 'webmock'
end
