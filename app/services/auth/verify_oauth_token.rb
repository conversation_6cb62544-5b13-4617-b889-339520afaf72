# frozen_string_literal: true

module Auth
  class VerifyOauthToken < ServiceBase
    OAUTH_TOKEN_TYPE = 'oauth2'

    attribute :authorization_header

    validates :authorization_header, presence: true

    def self.call(authorization_header:)
      new(authorization_header:).call
    end

    def call
      validate!

      token = extract_oauth_token
      decoded_jwt = DecodeJwt.call(token:)

      raise AuthorizationError, "Invalid token type '#{decoded_jwt.type}'" if decoded_jwt.type != OAUTH_TOKEN_TYPE

      true
    end

    private

    def extract_oauth_token
      pattern = /^Bearer /
      raise AuthenticationError, 'Missing Bearer token' unless authorization_header&.match(pattern)

      authorization_header.gsub(pattern, '')
    end
  end
end
