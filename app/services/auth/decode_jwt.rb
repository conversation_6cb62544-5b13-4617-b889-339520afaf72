# frozen_string_literal: true

module Auth
  class DecodeJwt < ServiceBase
    attribute :token, :string

    validates :token, presence: true

    def self.call(token:)
      new(token:).call
    end

    def call
      validate!

      decode_results = JWT.decode(token, rsa_public_key, true, jwt_options)

      Auth::DecodedToken.new(**decode_results.first.transform_keys(&:to_sym))
    rescue JWT::DecodeError, JWT::ExpiredSignature, JWT::InvalidIssuerError, JWT::InvalidAudError
      raise AuthorizationError, 'Invalid JWT token'
    end

    private

    def jwt_options
      {
        iss: jwt_config.issuer,
        verify_iss: true,
        aud: jwt_config.audience,
        verify_aud: true,
        verify_expiration: true,
        algorithm: jwt_config.algorithm
      }
    end

    def rsa_public_key
      return @rsa_public_key if defined? @rsa_public_key

      validate_public_key_path!

      jwt_public_key = File.read(jwt_config.public_key_path)
      @rsa_public_key = OpenSSL::PKey::RSA.new(jwt_public_key)
    rescue Errno::ENOENT
      raise JwtConfigurationError, "Invalid public_key for path: '#{jwt_config.public_key_path}'"
    rescue OpenSSL::PKey::RSAError => e
      raise JwtConfigurationError, e.message
    end

    def validate_public_key_path!
      return if jwt_config.public_key_path.present?

      raise JwtConfigurationError, 'Missing public_key_path'
    end

    def jwt_config
      Rails.application.config_for(:jwt)
    end
  end
end
