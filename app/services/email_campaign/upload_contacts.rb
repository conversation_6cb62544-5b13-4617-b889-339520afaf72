# frozen_string_literal: true

module EmailCampaign
  class UploadContacts < ServiceBase
    include Helpers

    attribute :contacts_file, type: ActionDispatch::Http::UploadedFile
    attribute :campaign_name, :string

    validates :contacts_file, presence: true
    validates :campaign_name, presence: true
    validate :contacts_file_must_be_valid
    validate :contacts_file_has_email_column
    validate :contacts_file_has_loan_id_column

    def call
      validate!
      upload_contacts_file
    end

    private

    def contacts_file_must_be_valid
      return unless contacts_file.content_type != 'text/csv'

      errors.add(:contacts_file, 'must be a CSV file')
    end

    def contacts_file_has_email_column
      return if contacts_file_content.first.key?(CSV_EMAIL_COLUMN_NAME)

      errors.add(:contacts_file, "must include an '#{CSV_EMAIL_COLUMN_NAME}' column")
    end

    def contacts_file_has_loan_id_column
      return if contacts_file_content.first.key?(CSV_LOAN_ID_COLUMN_NAME)

      errors.add(:contacts_file, "must include a '#{CSV_LOAN_ID_COLUMN_NAME}' column")
    end

    def contacts_file_content
      @contacts_file_content ||= begin
        csv_content = CSV.parse(contacts_file.read, headers: true)
        # If we do not rewind, a `The Content-Md5 you specified did not match what we received.` error will occur.
        contacts_file.rewind
        csv_content
      end
    end

    def upload_contacts_file
      Rails.logger.info("Uploading email campaign file to S3: #{s3_inputs}")
      s3_client.put_object(s3_inputs)
    end

    def s3_inputs
      {
        bucket: s3_bucket_name,
        key: s3_key(campaign_name),
        body: contacts_file
      }
    end
  end
end
