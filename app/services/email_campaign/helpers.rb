# frozen_string_literal: true

require 'csv'

module EmailCampaign
  module Helpers
    DEFAULT_DELIVERY_RATE = 250
    CSV_EMAIL_COLUMN_NAME = 'EMAIL'
    CSV_LOAN_ID_COLUMN_NAME = 'LOAN_ID'

    private

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def s3_key(campaign_name)
      "email_campaigns/#{campaign_name}.csv"
    end

    def parsed_contacts_file(campaign_name)
      contacts_file = get_contacts_file(campaign_name)
      CSV.parse(contacts_file.body.string, headers: true)
    end

    def get_contacts_file(campaign_name)
      s3_client.get_object(bucket: s3_bucket_name, key: s3_key(campaign_name))
    end

    def s3_bucket_name
      config.s3_bucket_name
    end

    def config
      Rails.application.config_for(:aws)
    end
  end
end
