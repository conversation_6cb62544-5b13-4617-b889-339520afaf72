# frozen_string_literal: true

class TemplateVariable
  include ActiveModel::API
  include ActiveModel::Attributes

  PHONE_PATTERN = /\A(\+\d{1,2}\s?)?1[-. ]?\(?\d{3}\)?[-. ]?\d{3}[-. ]?\d{4}\z/

  TYPES = [
    ARRAY_TYPE = :array,
    BOOLEAN_TYPE = :boolean,
    DATE_TYPE = :date,
    EMAIL_TYPE = :email,
    MONEY_TYPE = :money,
    NUMBER_TYPE = :number,
    PHONE_TYPE = :phone,
    STRING_TYPE = :string,
    FILE_TYPE = :file
  ].freeze

  SUBTYPES = [
    ARRAY_SUBTYPE = :array
  ].freeze

  attribute :default_value
  attribute :key
  attribute :name
  attribute :type
  attribute :subtype
  attribute :nullable, :boolean, default: false

  attr_reader :error

  validate :default_value_cannot_be_nil
  validate :default_value_cannot_be_invalid
  validates_presence_of :key, :name
  validates_inclusion_of :type, in: TYPES
  validates_inclusion_of :subtype, in: SUBTYPES, if: -> { subtype.present? }

  def valid_for?(input)
    return valid_nullable? if input.blank?

    subtype.present? ? validate_subtype?(input) : validate_type?(input)
  end

  def validate_subtype?(input)
    case subtype
    when ARRAY_SUBTYPE
      valid_subtype_array?(input)
    end
  end

  def validate_type?(input) # rubocop:disable Metrics/CyclomaticComplexity
    case type
    when ARRAY_TYPE
      valid_array?(input)
    when BOOLEAN_TYPE
      valid_boolean?(input)
    when DATE_TYPE
      valid_date?(input)
    when MONEY_TYPE
      valid_money?(input)
    when NUMBER_TYPE
      valid_number?(input)
    when PHONE_TYPE
      valid_phone?(input)
    when FILE_TYPE
      valid_file?(input)
    when EMAIL_TYPE, STRING_TYPE
      valid_string?(input)
    end
  end

  private

  def default_value_cannot_be_nil
    # Custom validator is required to permit 'false' as a valid default_value
    errors.add(:default_value, "can't be nil") if default_value.nil?
  end

  def default_value_cannot_be_invalid
    errors.add(:default_value, :invalid) if default_value != false && !valid_for?(default_value)
  end

  def valid_subtype_array?(input)
    return invalid_with_error('must be an array') unless valid_array?(input)

    @subtype_validation = true
    errors = input.filter_map.with_index do |value, index|
      error = validate_type?(value)
      "index[#{index}] #{error}" if error != true
    end

    @subtype_validation = false
    return true if errors.blank?

    invalid_with_error(errors.join(', '))
  end

  def valid_array?(input)
    return true if input.is_a?(Array)

    invalid_with_error('must be an array')
  end

  def valid_boolean?(input)
    return true if input.in?([true, false, 'true', 'false'])

    invalid_with_error('must be a boolean value or a boolean string')
  end

  def valid_date?(input)
    input.is_a?(Date) || Date.strptime(input, '%Y-%m-%d').present?
  rescue Date::Error
    invalid_with_error('must be a date in iso8601 format %Y-%m-%d')
  end

  def valid_money?(input)
    return true if input.is_a?(Numeric) && !input.negative?

    invalid_with_error('must be a numeric money amount')
  end

  def valid_nullable?
    return true if nullable

    invalid_with_error('may not be blank')
  end

  def valid_number?(input)
    return true if input.is_a?(Numeric)

    invalid_with_error('must be a number')
  end

  def valid_phone?(input)
    return true if input =~ PHONE_PATTERN

    invalid_with_error('must be a valid phone number')
  end

  def valid_string?(input)
    return true if input.is_a?(String)

    invalid_with_error('must be a string')
  end

  def valid_file?(input)
    values = input.symbolize_keys.values_at(:filename, :download_url, :type) if input.is_a?(Hash)
    return true if values&.all? { |value| value.is_a?(String)	&& value.present? }

    invalid_with_error('must be a Hash containing `filename`, `download_url`, and `type` keys')
  end

  def invalid_with_error(message)
    return message if @subtype_validation

    @error = message
    false
  end
end
