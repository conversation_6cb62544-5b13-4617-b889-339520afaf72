# frozen_string_literal: true

class AttributeFormatter
  class << self
    def call(inputs)
      formatted_inputs = inputs.each_with_object({}) do |(key, value), hash|
        template_variable = TemplateVariables::VARIABLES[key.to_sym]
        next if template_variable.type == TemplateVariable::FILE_TYPE

        hash[key.to_s] = formatted_from(template_variable.type, value)
      end

      formatted_inputs.merge(
        'TEXT_MESSAGE_OPT_OUT' => MessageConstants::TEXT_MESSAGE_OPT_OUT,
        'CUSTOMER_SERVICE_PHONE_NUMBER' => MessageConstants::CUSTOMER_SERVICE_PHONE_NUMBER
      )
    end

    private

    def formatted_from(type, input)
      case type
      when TemplateVariable::DATE_TYPE
        Date.strptime(input, '%Y-%m-%d').strftime('%m/%d/%Y')
      when TemplateVariable::MONEY_TYPE
        format('%.2f', input.to_f)
      else
        input
      end
    end
  end
end
