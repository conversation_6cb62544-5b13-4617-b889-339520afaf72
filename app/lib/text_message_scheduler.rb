# frozen_string_literal: true

class TextMessageScheduler
  class << self
    include LoggingConcern
    def call(message:)
      scheduler_strategy(message).call(message:)
    end

    def within_schedule?(message, hour_in_central_time = InCentralTime.now.hour)
      in_schedule = scheduler_strategy(message).within_schedule?(hour_in_central_time)

      log_details(method_name: __method__, message:, within_schedule?: in_schedule)
      in_schedule
    end

    private

    def scheduler_strategy(message)
      case message.template.detail_info.topic
      when Template::DetailInfo::ORIGINATIONS_TOPIC
        OriginationsScheduler
      else
        ServicingScheduler
      end
    end
  end
end
