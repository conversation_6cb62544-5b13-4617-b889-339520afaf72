# frozen_string_literal: true

class DownloadAttachments
  class << self
    def call(inputs, &builder)
      result = inputs.map do |key, value|
        template_variable = TemplateVariables::VARIABLES[key.to_sym]
        next unless template_variable.type == TemplateVariable::FILE_TYPE

        Array.wrap(value).map do |attachment|
          attachment.symbolize_keys!
          builder.call(
            Net::HTTP.get(URI(attachment[:download_url])),
            attachment[:filename],
            attachment[:type]
          )
        end
      end

      result.flatten.compact
    end
  end
end
