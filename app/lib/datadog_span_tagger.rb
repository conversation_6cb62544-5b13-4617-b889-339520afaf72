# frozen_string_literal: true

class DatadogSpanTagger
  EVENT_TYPES = [
    REQUEST_EVENT = 'RequestEvent',
    ASYNC_EVENT = 'AsyncEvent',
    AUDIT_EVENT = 'AuditEvent',
    API_EVENT = 'ApiEvent'
  ].freeze

  class << self
    def add_event_tags_to_span(event_type, payload)
      if tag_active_span?(event_type)
        # For Async and Request events, tag the active span, which corresponds to the job or request
        datadog_span = Datadog::Tracing.active_span
        if datadog_span
          add_tags(datadog_span, event_type, payload)
        else
          Rails.logger.warn("#{self}: No active datadog span for #{event_type}", payload)
        end
      else
        # For other event types, create a new span to hold the event tags
        span_name = "#{event_type} - #{payload[:name]}"
        Datadog::Tracing.trace(span_name) do |span|
          add_tags(span, event_type, payload)
        end
      end
    end

    def error(error)
      datadog_span = Datadog::Tracing.active_span
      if datadog_span
        datadog_span.set_error(error)
      else
        Rails.logger.warn("#{self}: No active datadog span - unable to set error on span")
      end
    end

    private

    def tag_active_span?(event_type)
      event_type.in?([ASYNC_EVENT, REQUEST_EVENT])
    end

    def add_tags(span, event_type, payload)
      payload = payload.merge(type: event_type)
      span.set_tags(flatten_payload(payload, 'event'))

      # Ensure the active trace gets ingested (otherwise sampling could take place)
      # https://docs.datadoghq.com/tracing/trace_collection/automatic_instrumentation/dd_libraries/ruby/#priority-sampling
      Datadog::Tracing.keep!
    end

    def flatten_payload(payload, parent_key = nil)
      payload.each_with_object({}) do |(key, value), result|
        new_key = parent_key ? "#{parent_key}.#{key}" : key.to_s

        if value.is_a?(Hash)
          result.merge!(flatten_payload(value, new_key))
        else
          result[new_key] = value
        end
      end
    end
  end
end
