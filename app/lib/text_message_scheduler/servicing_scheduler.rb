# frozen_string_literal: true

class TextMessageScheduler
  class ServicingScheduler
    include LoggingConcern
    class << self
      def call(message:)
        new(message:).call
      end

      def from_hour
        Rails.application.config_for(:general).text_message_window_central_time_from_hour[:servicing]
      end

      def within_schedule?(hour_in_central_time = InCentralTime.now.hour)
        hour_in_central_time >= from_hour && hour_in_central_time < until_hour
      end

      def until_hour
        Rails.application.config_for(:general).text_message_window_central_time_until_hour[:servicing]
      end
    end

    attr_reader :message

    def initialize(message:)
      @message = message
    end

    def call
      message.detail_info.schedule_at = schedule_at
      message.save!

      log_details(method_name: __method__, custom_msg: 'Setting Job', message:, schedule_at:)

      SendTextMessageJob.set(wait_until: schedule_at)
                        .perform_async(message.id)
    end

    private

    def after_hours?
      InCentralTime.now.hour >= self.class.until_hour
    end

    def schedule_at
      return @schedule_at if defined? @schedule_at
      return @schedule_at = send_later if after_hours?
      return @schedule_at = Time.zone.now if self.class.within_schedule?

      @schedule_at = InCentralTime.now.change(hour: self.class.from_hour) + rand(1...60).minutes
    end

    def send_later
      InCentralTime.now.tomorrow.change(hour: self.class.from_hour) + rand(1...60).minutes
    end
  end
end
