# frozen_string_literal: true

# Internal wrapper for interacting with the Sendgrid platform's v3 API.
# Docs: https://docs.sendgrid.com/api-reference/how-to-use-the-sendgrid-v3-api/
class SendgridApi
  class ResponseError < StandardError; end
  class RateLimitError < StandardError; end

  WHITELISTED_EMAIL_DOMAINS = Rails.application.config_for(:general)[:email_whitelisted_domains]

  class << self
    def send_message!(personalizations:, sg_template_id:, template:, attachments: [])
      Rails.logger.info("Triggering SendGrid message for sg_template_id: #{sg_template_id} " \
                        "with personalizations: #{personalizations} and topic: #{template.detail_info.topic}")

      return unless email_is_whitelisted?(personalizations:)

      mail = build_sendgrid_mail(sg_template_id, template, personalizations, attachments)

      Rails.logger.info("Sending payload to Sendgrid: #{mail.to_json}")

      # The Sendgrid API may return an empty response body
      parse_response!(client.mail._('send').post(request_body: mail.to_json))
    rescue StandardError => e
      Rails.logger.error("Error triggering email: #{e.message}")
      raise
    end

    def unsubscribed?(email)
      parse_response!(client.asm.suppressions.global._(email).get).present?
    rescue StandardError => e
      Rails.logger.error("Error retrieving status of email: #{e.message}")
      raise
    end

    def build_attachment(content:, filename:, type:)
      attachment = SendGrid::Attachment.new
      attachment.content = Base64.strict_encode64(content)
      attachment.filename = filename
      attachment.type = type
      attachment.disposition = 'attachment'
      attachment
    end

    def build_personalization(email:, template_data:, message_id:)
      personalization = SendGrid::Personalization.new
      personalization.add_to(SendGrid::Email.new(email:))
      personalization.add_dynamic_template_data(template_data)
      personalization.add_custom_arg(SendGrid::CustomArg.new(key: 'message_id', value: message_id))
      personalization
    end

    private

    def build_sendgrid_mail(sg_template_id, template, personalizations, attachments)
      mail = SendGrid::Mail.new
      mail.from = from_address(template.detail_info.topic)
      mail.template_id = sg_template_id
      mail.asm = SendGrid::ASM.new(group_id: config.asm_group, groups_to_display: [])
      enable_list_bypassing(mail) if template.detail_info.override_suppression
      add_attachments_and_personalizations(mail, template, personalizations, attachments)
      mail
    end

    def add_attachments_and_personalizations(mail, template, personalizations, attachments)
      attachments ||= []
      attachments << build_vcf_card if template.detail_info.attach_vcf_card
      attachments.each { |attachment| mail.add_attachment(attachment) }
      personalizations.each { |personalization| mail.add_personalization(personalization) }
    end

    def enable_list_bypassing(mail)
      mail.mail_settings = SendGrid::MailSettings.new.tap do |mail_settings|
        mail_settings.bypass_list_management = SendGrid::BypassListManagement.new(enable: true)
      end
    end

    def parse_response!(response, success_statuses = (200..299).to_a)
      Rails.logger.info("Received #{response.status_code} response with body: #{response.body}")

      validate_response(response, success_statuses)

      JSON.parse(response.body) if response.body.present?
    end

    def validate_response(response, success_statuses)
      response_code = response.status_code.to_i
      return if success_statuses.include?(response_code)

      Rails.logger.error('Error from Sendgrid API: ' \
                         "Response body: #{response.body.to_json}; " \
                         "Response headers: #{response.headers.to_json}")

      raise RateLimitError, 'Request Ratelimited by Sendgrid.' if response_code == 429

      raise ResponseError, "Received #{response_code} response."
    end

    def from_address(topic)
      case topic
      when Template::DetailInfo::DELINQUENCY_TOPIC
        SendGrid::Email.new(email: '<EMAIL>', name: 'DELINQUENCY')
      when Template::DetailInfo::SERVICING_TOPIC
        SendGrid::Email.new(email: '<EMAIL>', name: 'Above Lending')
      else
        SendGrid::Email.new(email: '<EMAIL>', name: 'Above Lending')
      end
    end

    def build_vcf_card
      build_attachment(
        content: Rails.root.join('app/assets/vcf/Above Lending Support.vcf').read,
        filename: 'Above Lending Support.vcf',
        type: 'text/vcard'
      )
    end

    def email_is_whitelisted?(personalizations:)
      return true if all_emails_whitelisted?(personalizations:)

      Rails.logger.warn("Email with personalizations: #{personalizations}  is not whitelisted.")
      false
    end

    def all_emails_whitelisted?(personalizations:)
      return true if Rails.env.production?

      emails = personalizations.flat_map do |personalization|
        personalization.tos.map { |tos| tos['email'].downcase }
      end

      emails.all? do |email|
        email.end_with?(*WHITELISTED_EMAIL_DOMAINS)
      end
    end

    def client
      SendGrid::API.new(api_key: config.api_key).client
    end

    def config
      Rails.application.config_for(:sendgrid)
    end
  end
end
