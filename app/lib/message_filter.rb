# frozen_string_literal: true

class MessageFilter
  SINGULAR_DETAIL_INFO_FILTERS = %w[recipient status delivery_method].freeze
  PLURAL_DETAIL_INFO_FILTERS = SINGULAR_DETAIL_INFO_FILTERS.map(&:pluralize)
  NEGATION_DETAIL_INFO_FILTERS = SINGULAR_DETAIL_INFO_FILTERS.map { |detail_info_attr| "not_#{detail_info_attr}" }
  DETAIL_INFO_FILTERS = (SINGULAR_DETAIL_INFO_FILTERS + PLURAL_DETAIL_INFO_FILTERS + NEGATION_DETAIL_INFO_FILTERS)
  ATTRIBUTION_FILTERS = [
    ID_ATTRIBUTION_FILTER = 'attribution_id',
    TYPE_ATTRIBUTION_FILTER = 'attribution_type'
  ].freeze
  TEMPLATE_FILTERS = [
    TEMPLATE_KEY_FILTER = 'template_key',
    PLURAL_TEMPLATE_KEY_FILTER = TEMPLATE_KEY_FILTER.pluralize
  ].freeze
  TIMESTAMP_FILTERS = [
    CREATED_BEFORE_FILTER = 'created_before',
    CREATED_AFTER_FILTER = 'created_after',
    UPDATED_BEFORE_FILTER = 'updated_before',
    UPDATED_AFTER_FILTER = 'updated_after'
  ].freeze

  SUPPORTED_FILTERS = DETAIL_INFO_FILTERS + TEMPLATE_FILTERS + TIMESTAMP_FILTERS + ATTRIBUTION_FILTERS

  attr_reader :errors

  def initialize(filters)
    @filters = filters
  end

  def valid?
    unsupported_filters = @filters.keys.map(&:to_s) - SUPPORTED_FILTERS
    return true if unsupported_filters.blank?

    @errors = unsupported_filters.map { |e| { message: "#{e} is not a supported filter" } }
    false
  end

  def call(scope)
    @filters.reduce(scope) do |scope_builder, filter_and_value|
      filter, value = filter_and_value

      case filter.to_s
      when *ATTRIBUTION_FILTERS
        filter_by_attribution(scope_builder, filter, value)
      when *DETAIL_INFO_FILTERS
        filter_by_detail_info(scope_builder, filter, value)
      when *TEMPLATE_FILTERS
        filter_by_template(scope_builder, filter, value)
      when *TIMESTAMP_FILTERS
        filter_by_timestamp(scope_builder, filter, value)
      end
    end
  end

  def filter_by_attribution(scope_builder, filter, value)
    case filter.to_s
    when ID_ATTRIBUTION_FILTER
      scope_builder.where("detail_info->'entities' @> :json", { json: [{ id: value }].to_json })
    when TYPE_ATTRIBUTION_FILTER
      scope_builder.where("detail_info->'entities' @> :json", { json: [{ type: value.upcase }].to_json })
    end
  end

  def filter_by_detail_info(scope_builder, filter, value)
    case filter.to_s
    when *SINGULAR_DETAIL_INFO_FILTERS
      scope_builder.where('detail_info->>:filter = :value', { filter:, value: })
    when *PLURAL_DETAIL_INFO_FILTERS
      scope_builder.where('detail_info->>:filter IN (:value)', { filter: filter.to_s.singularize, value: })
    when *NEGATION_DETAIL_INFO_FILTERS
      scope_builder.where('detail_info->>:filter != :value', { filter: filter[4..], value: })
    end
  end

  def filter_by_template(scope_builder, filter, value)
    case filter.to_s
    when TEMPLATE_KEY_FILTER
      template = Template.find_by("detail_info->>'key' = ?", value)
      scope_builder.where(template:)
    when PLURAL_TEMPLATE_KEY_FILTER
      templates = Template.where("detail_info->>'key' IN (?)", value)
      scope_builder.where(template: templates)
    end
  end

  def filter_by_timestamp(scope_builder, filter, value)
    # These conditions cannot be constructed dynamically without triggering Brakeman's SQL injection attack warning.
    case filter.to_s
    when CREATED_BEFORE_FILTER
      scope_builder.where('created_at < :value', { value: })
    when CREATED_AFTER_FILTER
      scope_builder.where('created_at > :value', { value: })
    when UPDATED_BEFORE_FILTER
      scope_builder.where('updated_at < :value', { value: })
    when UPDATED_AFTER_FILTER
      scope_builder.where('updated_at > :value', { value: })
    end
  end
end
