# frozen_string_literal: true

# A facade for interacting with the Solution's by Text v2 platform's HTTP API.
# Docs: https://developers.solutionsbytext.com/docs/t2c2.0/index.html
class SbtApi
  class ResponseError < StandardError; end

  SUBSCRIBER_STATUSES = [
    ACTIVE_SUBSCRIBER_STATUS = 'Active',
    INACTIVE_SUBSCRIBER_STATUS = 'InActive'
  ].freeze

  class << self
    def valid_phone_number?(phone_number)
      Rails.logger.info("#{name} - Checking if phone number #{phone_number} is valid.")

      params = { msisdn: phone_number }
      parsed_response = parse_response!(send_get_request!('/phonenumbers-data', params))

      # A type of 'Invalid' or 'Landline' is returned for an invalid or non-mobile number, respectively.
      parsed_response.dig('data', 0, 'type') == 'Mobile'
    end

    def get_status(phone_number)
      Rails.logger.info("#{name} - Checking the status of the following phone number: #{phone_number}")

      params = { msisdn: phone_number }
      parse_response!(send_get_request!("/groups/#{config.group_id}/subscribers/status", params))
    end

    def add_subscriber!(phone_number)
      Rails.logger.info("#{name} - Adding subscriber for #{phone_number}.")

      body = { msisdn: phone_number, verification: { type: 'Optin', force: false } }
      raw_response = send_post_request!("/groups/#{config.group_id}/subscribers", body)
      # A 409 response is returned in the case when a number is already a registered subscriber.
      parsed_response = parse_response!(raw_response, [*200..299, 409])

      verify_new_subscriber_success!(parsed_response)
    end

    def send_message!(phone_number, message_body)
      Rails.logger.info("#{name} - Sending message to #{phone_number}. #{message_body}")
      body = { message: message_body, messageType: 'Unicast', subscribers: [{ msisdn: phone_number }] }
      return SecureRandom.uuid if !Rails.env.production? && Flipper.enabled?(:disable_sending_sms)

      parsed_response = parse_response!(send_post_request!("/groups/#{config.group_id}/messages",
                                                           body))
      parse_sent_message_response(parsed_response)
    end

    private

    def send_get_request!(path, params = {})
      Rails.logger.info("#{name} - Sending GET to #{path}")
      Rails.logger.debug("#{name} - Sending GET with params #{params}")

      Faraday.get(URI.join(normalized_url(config.api_base_url), path), params, default_headers)
    end

    def send_post_request!(path, body = {})
      Rails.logger.info("#{name} - Sending POST to #{path}")
      Rails.logger.debug("#{name} - Sending POST with params #{body}")

      Faraday.post(URI.join(normalized_url(config.api_base_url), path), body.to_json, default_headers)
    end

    def normalized_url(url)
      # Ensures a trailing '/' is always present on the base URL value. URI.join drops the last path element of
      # "relative" URL when provided as the base URL. Since this value is controlled through an environment variable,
      # we should handle the case when a trailing slash is omitted gracefully. See
      # https://apidock.com/ruby/URI/join/class for more detail.
      "#{url.chomp('/')}/"
    end

    def parse_response!(response, success_statuses = (200..299).to_a)
      Rails.logger.info("#{name} - Received #{response.status} response")
      Rails.logger.debug("#{name} - Response body: #{response.body}")

      unless success_statuses.include?(response.status)
        raise ResponseError,
              "Received #{response.status} response. Response Body: #{response.body}"
      end

      JSON.parse(response.body)
    end

    def default_headers
      {
        'Authorization' => authorization,
        'Content-Type' => 'application/json'
      }
    end

    def authorization
      Rails.cache.fetch('sbt_api_authorization', expires_in: 59.minutes) { generate_token }
    end

    def verify_new_subscriber_success!(response)
      # Docs: https://developers.solutionsbytext.com/docs/t2c2.0/index.html#tag/Error-Codes
      success_app_codes = [
        'gen.1200', # Request successful
        'sub.1019'  # Subscriber already exists
      ]
      return if success_app_codes.include?(response['appCode'])

      raise ResponseError, "Failed to add new subscriber. Response Body: #{response.to_json}"
    end

    def parse_sent_message_response(response)
      sbt_message_id = response.dig('data', 'messageId')
      return sbt_message_id if sbt_message_id.present?

      raise ResponseError, "Failed to trigger delivery of SMS message. Response Body: #{response.to_json}"
    end

    def generate_token
      url = URI.join(normalized_url(config.auth_base_url), '/connect/token')
      headers = { 'Content-Type' => 'application/x-www-form-urlencoded' }
      body = { grant_type: 'client_credentials', client_id: config.client_id, client_secret: config.client_secret }

      response = Faraday.post(url, URI.encode_www_form(body), headers)

      return nil unless response.status == 200

      parsed_response = JSON.parse(response.body)
      "#{parsed_response['token_type']} #{parsed_response['access_token']}"
    end

    def config
      Rails.application.config_for(:sbt)
    end
  end
end
