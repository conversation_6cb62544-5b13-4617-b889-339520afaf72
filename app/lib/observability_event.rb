# frozen_string_literal: true

module ObservabilityEvent
  extend ActiveSupport::Concern

  class_methods do
    def success!(meta = {})
      notify!(success: true, meta:)
    end

    def failure!(reason:, meta: {})
      notify!(success: false, reason:, meta:)
    end

    private

    def notify!(payload)
      event_name = ancestors.first.name

      # Record the event in datadog as well. Eventually event tracking will be refactored to look more
      # like AMS and record events in datadog only.
      DatadogSpanTagger.add_event_tags_to_span(event_type_for(event_name), payload.merge(name: event_name))
    end

    # This is a simplistic way to determine the event type until we refactor event tracking in
    # comms service to look more like AMS, which passes in the event type.
    def event_type_for(event_name)
      event_name.end_with?('Job') ? DatadogSpanTagger::ASYNC_EVENT : DatadogSpanTagger::AUDIT_EVENT
    end
  end

  def success!(meta = {})
    self.class.success!(meta)
  end

  def failure!(reason:, meta: {})
    self.class.failure!(reason:, meta:)
  end
end
