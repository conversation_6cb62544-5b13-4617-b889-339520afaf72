# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength
class TemplateVariables
  class << self
    private

    def address_line1
      TemplateVariable.new(
        default_value: '123 Main St',
        key: :address_line1,
        name: 'Borrower Address Line 1',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def address_line2
      TemplateVariable.new(
        default_value: 'Apt 24B',
        key: :address_line2,
        name: 'Borrower Address Line 2',
        type: TemplateVariable::STRING_TYPE,
        nullable: true
      )
    end

    def address_city
      TemplateVariable.new(
        default_value: 'Chicago',
        key: :address_city,
        name: 'Borrower Address City',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def address_state
      TemplateVariable.new(
        default_value: 'IL',
        key: :address_state,
        name: 'Borrower Address State',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def address_zip_code
      TemplateVariable.new(
        default_value: '60606',
        key: :address_zip_code,
        name: '<PERSON>rrower Address Zip Code',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def applicant_address
      TemplateVariable.new(
        default_value: '123 Main St, Apt 24B',
        key: :applicant_address,
        name: 'Applicant Address for NOAA',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def applicant_city_state_zip
      TemplateVariable.new(
        default_value: 'Chicago, IL 60606',
        key: :applicant_city_state_zip,
        name: 'Applicant City, State, Zip for NOAA',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def applicant_name
      TemplateVariable.new(
        default_value: 'John Doe',
        key: :applicant_name,
        name: 'Applicant Name for NOAA',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def bank_name
      TemplateVariable.new(
        default_value: 'Humans Credit Union',
        key: :bank_name,
        name: 'Bank Name',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def code
      TemplateVariable.new(
        default_value: 'XXXXX',
        key: :code,
        name: 'Code',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def auto_payment_type
      TemplateVariable.new(
        default_value: 'AutoPay',
        key: :auto_payment_type,
        name: 'Auto Payment Type',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def contract_date
      TemplateVariable.new(
        default_value: '2023-06-02'.to_date,
        key: :contract_date,
        name: 'Contract Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def credit_report_date
      TemplateVariable.new(
        default_value: '06/14/2024',
        key: :credit_report_date,
        name: 'Credit Report Date',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def date
      TemplateVariable.new(
        default_value: '06/14/2024',
        key: :date,
        name: 'Date of NOAA/NOIA generation',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def days_past_due
      TemplateVariable.new(
        default_value: 107,
        key: :days_past_due,
        name: 'Number of Days Past Due',
        type: TemplateVariable::NUMBER_TYPE
      )
    end

    def display_oh_discrimination_disclosure
      TemplateVariable.new(
        default_value: false,
        key: :display_oh_discrimination_disclosure,
        name: 'Display Ohio Discrimination Disclosure in NOAA email',
        type: TemplateVariable::BOOLEAN_TYPE,
        nullable: true
      )
    end

    def email
      TemplateVariable.new(
        default_value: '<EMAIL>',
        key: :email,
        name: 'Email Address',
        type: TemplateVariable::EMAIL_TYPE
      )
    end

    def expiration_date
      TemplateVariable.new(
        default_value: '06/14/2024',
        key: :expiration_date,
        name: 'Date of NOIA expiration',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def factors
      TemplateVariable.new(
        default_value: [
          { factor: 'Serious delinquency' },
          { factor: 'Number of accounts with delinquency' }
        ],
        key: :factors,
        name: 'Factors affecting credit score',
        type: TemplateVariable::ARRAY_TYPE,
        nullable: true
      )
    end

    def failure_date
      TemplateVariable.new(
        default_value: '2024-06-01'.to_date,
        key: :failure_date,
        name: 'Payment Failure Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def first_name
      TemplateVariable.new(
        default_value: 'Erica',
        key: :first_name,
        name: 'Borrower First Name',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def first_payment_on
      TemplateVariable.new(
        default_value: '2024-06-01'.to_date,
        key: :first_payment_on,
        name: 'First Payment On',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def loan_maturity_date
      TemplateVariable.new(
        default_value: '2025-11-08'.to_date,
        key: :loan_maturity_date,
        name: 'Loan Maturity Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def full_due_amount
      TemplateVariable.new(
        default_value: 732.93,
        key: :full_due_amount,
        name: 'Amount Due on Next Payment Date',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def full_name
      TemplateVariable.new(
        default_value: 'Erica Lambert',
        key: :full_name,
        name: 'Borrower Full Name',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def gds_decline_reason
      TemplateVariable.new(
        default_value: 'Low FICO score',
        key: :gds_decline_reason,
        name: 'GDS Decline Reason resulting in NOAA',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def gds_decline_reasons
      TemplateVariable.new(
        default_value: [
          { reason: 'Low FICO score' },
          { reason: 'Loan to income ratio' }
        ],
        key: :gds_decline_reasons,
        name: 'GDS Decline Reasons resulting in NOAA',
        type: TemplateVariable::ARRAY_TYPE
      )
    end

    def gds_score
      TemplateVariable.new(
        default_value: 486,
        key: :gds_score,
        name: 'GDS Score resulting in NOAA',
        type: TemplateVariable::NUMBER_TYPE
      )
    end

    def investor_name
      TemplateVariable.new(
        default_value: 'Above Funding Trust',
        key: :investor_name,
        name: 'Investor Name',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def is_crb # rubocop:disable Naming/PredicateName
      TemplateVariable.new(
        default_value: true,
        key: :is_crb,
        name: 'Is CRB',
        type: TemplateVariable::BOOLEAN_TYPE,
        nullable: true
      )
    end

    def is_equifax # rubocop:disable Naming/PredicateName
      TemplateVariable.new(
        default_value: true,
        key: :is_equifax,
        name: 'Is Equifax',
        type: TemplateVariable::BOOLEAN_TYPE,
        nullable: true
      )
    end

    def is_fcra # rubocop:disable Naming/PredicateName
      TemplateVariable.new(
        default_value: true,
        key: :is_fcra,
        name: 'Is Fcra',
        type: TemplateVariable::BOOLEAN_TYPE,
        nullable: true
      )
    end

    def itemization_date_from_contract_date
      TemplateVariable.new(
        default_value: '2023-07-21'.to_date,
        key: :itemization_date_from_contract_date,
        name: 'Itemization Contract Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def itemization_date_from_payment_date
      TemplateVariable.new(
        default_value: '2023-07-21'.to_date,
        key: :itemization_date_from_payment_date,
        name: 'Itemization Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def last_four
      TemplateVariable.new(
        default_value: 8765,
        key: :last_four,
        name: 'Last four digits of bank account or routing number',
        type: TemplateVariable::NUMBER_TYPE
      )
    end

    def last_name
      TemplateVariable.new(
        default_value: 'Lambert',
        key: :last_name,
        name: 'Borrower Last Name',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def last_payment_amount
      TemplateVariable.new(
        default_value: 100.21,
        key: :last_payment_amount,
        name: 'Last Payment Amount',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def last_payment_due_date
      TemplateVariable.new(
        default_value: '2023-07-28'.to_date,
        key: :last_payment_due_date,
        name: 'Last Payment Due Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def link
      TemplateVariable.new(
        default_value: 'https://abovelending.com',
        key: :link,
        name: 'Link',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def loan_agreement_file
      TemplateVariable.new(
        default_value: {
          download_url: 'https://abovelending.com',
          filename: 'Loan Agreement.pdf',
          type: 'application/pdf'
        },
        key: :loan_agreement_file,
        name: 'Loan Agreement File',
        type: TemplateVariable::FILE_TYPE
      )
    end

    def loan_agreement_files
      TemplateVariable.new(
        default_value: [{
          download_url: 'https://abovelending.com',
          filename: 'Loan Agreement.pdf',
          type: 'application/pdf'
        }],
        key: :loan_agreement_files,
        name: 'Loan Agreement Files',
        type: TemplateVariable::FILE_TYPE,
        subtype: TemplateVariable::ARRAY_SUBTYPE
      )
    end

    def loan_display_id
      TemplateVariable.new(
        default_value: '12345678',
        key: :loan_display_id,
        name: 'Loan Display ID',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def loan_term
      TemplateVariable.new(
        default_value: 72,
        key: :loan_term,
        name: 'Loan term',
        type: TemplateVariable::NUMBER_TYPE
      )
    end

    def lp_loan_status
      TemplateVariable.new(
        default_value: 'Active',
        key: :lp_loan_status,
        name: 'Contract Status',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def message_send_date
      TemplateVariable.new(
        default_value: '2023-08-15'.to_date,
        key: :message_send_date,
        name: 'Date of Message Send',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def message_send_date_plus28
      TemplateVariable.new(
        default_value: '2023-09-12'.to_date,
        key: :message_send_date_plus28,
        name: '28 Days After Message Send Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def message_send_date_plus29
      TemplateVariable.new(
        default_value: '2023-09-13'.to_date,
        key: :message_send_date_plus29,
        name: '29 Days After Message Send Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def message_send_date_plus30
      TemplateVariable.new(
        default_value: '2023-09-14'.to_date,
        key: :message_send_date_plus30,
        name: '30 Days After Message Send Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def next_payment_date
      TemplateVariable.new(
        default_value: '2023-08-21'.to_date,
        key: :next_payment_date,
        name: 'Next Payment Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def next_scheduled_payment_amount
      TemplateVariable.new(
        default_value: 100.23,
        key: :next_scheduled_payment_amount,
        name: 'Next Scheduled Payment Amount',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def offer_amount
      TemplateVariable.new(
        default_value: '$0.00',
        key: :offer_amount,
        name: 'Offer Amount',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def offers
      TemplateVariable.new(
        default_value: [
          {
            lender: 'Above Lending',
            amount: 10_000,
            term: 'Monthly',
            monthly_payment: 1000,
            interest_rate: '23%',
            apr: 23,
            origination_fee: 500
          }
        ],
        key: :offers,
        name: 'List of offers available to borrower',
        type: TemplateVariable::ARRAY_TYPE
      )
    end

    def originator_name
      TemplateVariable.new(
        default_value: 'Above Lending',
        key: :originator_name,
        name: 'Originator Name',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def past_payment_amounts_list
      TemplateVariable.new(
        default_value: [345.67, 345.67, 345.67],
        key: :past_payment_amounts_list,
        name: 'List of Past Payment Amounts',
        type: TemplateVariable::ARRAY_TYPE
      )
    end

    def past_payment_dates_list
      TemplateVariable.new(
        default_value: %w[2023-05-27 2023-06-29 2023-07-28],
        key: :past_payment_dates_list,
        name: 'List of Past Payment Due Dates',
        type: TemplateVariable::ARRAY_TYPE
      )
    end

    def past_payment_list
      TemplateVariable.new(
        default_value: [
          {
            date: '2023-05-27',
            amount: 100.00
          },
          {
            date: '2023-06-29',
            amount: 100.00
          }
        ],
        key: :past_payment_list,
        name: 'List of Past Payments',
        type: TemplateVariable::ARRAY_TYPE
      )
    end

    def payment_amount
      TemplateVariable.new(
        default_value: 200.50,
        key: :payment_amount,
        name: 'Payment Amount',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def payment_date
      TemplateVariable.new(
        default_value: '2023-08-21'.to_date,
        key: :payment_date,
        name: 'Generic Payment Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def payment_frequency
      TemplateVariable.new(
        default_value: 'Monthly',
        key: :payment_frequency,
        name: 'Payment Frequency',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def phone_number
      TemplateVariable.new(
        default_value: '15554443333',
        key: :phone_number,
        name: 'Phone Number',
        type: TemplateVariable::PHONE_TYPE
      )
    end

    def return_code
      TemplateVariable.new(
        default_value: 'R100',
        key: :return_code,
        name: 'LoanPro Payment Return Code',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def service_entity_name
      TemplateVariable.new(
        default_value: 'NA',
        key: :service_entity_name,
        name: 'Service Entity Name',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def service_entity_shortcode
      TemplateVariable.new(
        default_value: 'bf',
        key: :service_entity_name,
        name: 'Service Entity Shortcode',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def subject
      TemplateVariable.new(
        default_value: 'NA',
        key: :subject,
        name: 'Subject',
        type: TemplateVariable::STRING_TYPE,
        nullable: true
      )
    end

    def token
      TemplateVariable.new(
        default_value: 'NA',
        key: :token,
        name: 'Token',
        type: TemplateVariable::STRING_TYPE
      )
    end

    def total_amount_due
      TemplateVariable.new(
        default_value: 609.93,
        key: :total_amount_due,
        name: 'Total Amount Due',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def total_credited
      TemplateVariable.new(
        default_value: 1472.86,
        key: :total_credited,
        name: 'Total Credited',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def total_interest_due
      TemplateVariable.new(
        default_value: 483.62,
        key: :total_interest_due,
        name: 'Total Interest Due',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def total_payoff_amount
      TemplateVariable.new(
        default_value: 22_637.47,
        key: :total_payoff_amount,
        name: 'Total Payoff Amount',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def total_payoff_amount_less_interest
      TemplateVariable.new(
        default_value: 2817.93,
        key: :total_payoff_amount_less_interest,
        name: 'Total Payoff Amount Excluding Interest',
        type: TemplateVariable::MONEY_TYPE
      )
    end

    def unified_id
      TemplateVariable.new(
        default_value: 111_111_11,
        key: :unified_id,
        name: 'Unified id',
        type: TemplateVariable::NUMBER_TYPE
      )
    end

    def charge_off_date
      TemplateVariable.new(
        default_value: '2023-05-19'.to_date,
        key: :charge_off_date,
        name: 'Charge off Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def charge_off_balance
      TemplateVariable.new(
        default_value: 1199.11,
        key: :charge_off_balance,
        name: 'Charge Off Balance',
        type: TemplateVariable::NUMBER_TYPE
      )
    end

    def balance
      TemplateVariable.new(
        default_value: 1199.11,
        key: :balance,
        name: 'Balance',
        type: TemplateVariable::NUMBER_TYPE
      )
    end

    def sale_date
      TemplateVariable.new(
        default_value: '2023-01-19'.to_date,
        key: :sale_date,
        name: 'Sale Date',
        type: TemplateVariable::DATE_TYPE
      )
    end

    def last_4_unified_id
      TemplateVariable.new(
        default_value: 1111,
        key: :last_4_unified_id,
        name: 'Last4 unified id',
        type: TemplateVariable::NUMBER_TYPE
      )
    end
  end

  VARIABLES = {
    address_line1:,
    address_line2:,
    address_city:,
    address_state:,
    address_zip_code:,
    applicant_address:,
    applicant_city_state_zip:,
    applicant_name:,
    auto_payment_type:,
    bank_name:,
    code:,
    contract_date:,
    credit_report_date:,
    date:,
    days_past_due:,
    display_oh_discrimination_disclosure:,
    email:,
    expiration_date:,
    factors:,
    failure_date:,
    first_name:,
    first_payment_on:,
    loan_maturity_date:,
    full_due_amount:,
    full_name:,
    gds_decline_reason:,
    gds_decline_reasons:,
    gds_score:,
    investor_name:,
    is_crb:,
    is_equifax:,
    is_fcra:,
    itemization_date_from_contract_date:,
    itemization_date_from_payment_date:,
    last_four:,
    last_name:,
    last_payment_amount:,
    last_payment_due_date:,
    link:,
    loan_agreement_file:,
    loan_agreement_files:,
    loan_display_id:,
    loan_term:,
    lp_loan_status:,
    message_send_date:,
    message_send_date_plus28:,
    message_send_date_plus29:,
    message_send_date_plus30:,
    next_payment_date:,
    next_scheduled_payment_amount:,
    offers:,
    offer_amount:,
    originator_name:,
    past_payment_amounts_list:,
    past_payment_dates_list:,
    past_payment_list:,
    payment_amount:,
    payment_date:,
    payment_frequency:,
    phone_number:,
    return_code:,
    service_entity_name:,
    service_entity_shortcode:,
    subject:,
    token:,
    total_amount_due:,
    total_credited:,
    total_interest_due:,
    total_payoff_amount:,
    total_payoff_amount_less_interest:,
    unified_id:,
    charge_off_date:,
    charge_off_balance:,
    balance:,
    sale_date:,
    last_4_unified_id:
  }.freeze
end

# rubocop:enable Metrics/ClassLength
