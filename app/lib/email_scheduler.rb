# frozen_string_literal: true

class EmailScheduler
  include LoggingConcern
  class << self
    def call(message:)
      new(message:).call
    end

    def topic_window(topic)
      if topic == Template::DetailInfo::DELINQUENCY_TOPIC
        [
          Rails.application.config_for(:general).informative_email_window_central_time_from_hour,
          Rails.application.config_for(:general).informative_email_window_central_time_until_hour
        ]
      else
        [0, 24]
      end
    end
  end

  attr_reader :message, :from_hour, :until_hour, :topic

  def initialize(message:)
    @message = message
    @topic = message.template.detail_info.topic
    @from_hour, @until_hour = self.class.topic_window(@topic)
  end

  def call
    message.detail_info.schedule_at = schedule_at
    message.save!

    log_details(method_name: __method__, custom_msg: 'Setting Job', message:, schedule_at:)

    SendEmailJob.set(wait_until: schedule_at)
                .perform_async(message.id)
  end

  def within_schedule?(hour_in_central_time = InCentralTime.now.hour)
    hour_in_central_time >= from_hour && hour_in_central_time < until_hour
  end

  private

  def after_hours?
    InCentralTime.now.hour >= until_hour
  end

  def schedule_at
    return Time.zone.now unless Rails.env.production?

    @schedule_at ||= calculate_schedule_time
  end

  def calculate_schedule_time
    return Time.zone.now if within_schedule?
    return schedule_for_tomorrow if after_hours?

    schedule_for_today
  end

  def schedule_for_tomorrow
    InCentralTime.now.tomorrow.change(hour: from_hour) + rand(1...60).minutes
  end

  def schedule_for_today
    InCentralTime.now.change(hour: from_hour) + rand(1...60).minutes
  end
end
