# frozen_string_literal: true

class ApiController < ActionController::API
  include Pagy::Backend

  # Param wrapping depends on parameters existing as top level keys of the associated
  # model, but because we nest fields in jsonb detail_info fields these attribute names
  # are not available so we decided to disable param wrapping
  # https://api.rubyonrails.org/v7.0.4.2/classes/ActionController/ParamsWrapper.html
  wrap_parameters false

  before_action :verify_auth

  rescue_from Auth::AuthenticationError do |e|
    render_error(e.message, e.status)
  end

  rescue_from Auth::AuthorizationError do |e|
    render_error(e.message, e.status)
  end

  private

  def render_error(errors, status)
    Rails.logger.error('API endpoint error.', errors:, status:)
    render json: { errors: }, status:
  end

  def verify_auth
    Auth::VerifyOauthToken.call(authorization_header: request.authorization)
  rescue ActiveModel::ValidationError => e
    raise Auth::AuthenticationError, e.message
  end
end
