# frozen_string_literal: true

module Utils
  class EmailCampaignsController < ApiController
    # This API endpoint is called directly by engineers from postman without an OAuth token or Identity Service
    # authorized header. These API endpoints MUST NOT BE EXPOSED PUBLICLY.
    skip_before_action :verify_auth

    # PUT /utils/:campaign_name/contacts_file
    def upload_contacts_file
      uploader = EmailCampaign::UploadContacts.new(upload_contacts_file_params)

      if uploader.valid?
        uploader.call
        render json: {
          message: "Contacts file uploaded successfully for campaign #{upload_contacts_file_params[:campaign_name]}"
        }, status: :ok
      else
        render json: { errors: uploader.errors.full_messages }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error("Error uploading contacts file: #{e.message}, backtrace: #{e.backtrace}")
      render json: { error: e.message }, status: :internal_server_error
    end

    # POST /utils/:campaign_name/trigger
    def trigger_campaign
      campaign_name, template_name, delivery_rate = trigger_campaign_params.values
      job_id = EmailCampaignJob::Coordinator.perform_async(campaign_name, template_name, delivery_rate&.to_i)

      render json: {
        message: "Email campaign job #{campaign_name} with template: #{template_name} successfully enqueued",
        job_id:
      }, status: :ok
    end

    private

    def upload_contacts_file_params
      params.permit(:campaign_name, :contacts_file)
    end

    def trigger_campaign_params
      params.permit(:campaign_name, :template_name, :delivery_rate)
    end
  end
end
