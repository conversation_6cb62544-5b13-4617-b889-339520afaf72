# frozen_string_literal: true

class ApplicationController < ActionController::Base
  # NOTE: we are intentionally disabling CSRF protection for the route_not_found action
  #       This will allow POST requests to non-existing routes to be handled
  #       without triggering the InvalidAuthenticityToken error.
  skip_before_action :verify_authenticity_token, only: [:route_not_found]

  rescue_from ActionDispatch::Http::Parameters::ParseError do |exception|
    render status: 400, json: { errors: [exception.message] }
  end

  def route_not_found
    render json: { error: 'Not Found' }, status: :not_found
  end
end
