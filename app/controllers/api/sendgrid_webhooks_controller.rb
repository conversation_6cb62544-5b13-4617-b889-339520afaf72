# frozen_string_literal: true

module Api
  class SendgridWebhooksController < ApiController
    include ObservabilityEvent
    include LoggingConcern

    def create
      params.require(:_json).each do |event_payload|
        next if unrecognized_event_type?(event_payload) || missing_message_id?(event_payload)

        event_params = event_payload.permit(:event, :message_id, :timestamp)

        log_details(method_name: __method__, custom_msg: 'Setting Job',
                    message_id: event_params[:message_id], event: event_params[:event].downcase)

        SendgridEmailDeliveryEventJob.perform_async(
          event_params[:event].downcase, event_params[:message_id],
          event_params[:timestamp], event_payload.permit!.to_json
        )
      end

      head :accepted
    end

    private

    def unrecognized_event_type?(event_payload)
      event = event_payload[:event]&.downcase

      return false if SendgridEmailDeliveryEventJob::SUPPORTED_EVENT_TYPES.include?(event)

      log_details(method_name: __method__, custom_msg: 'The following Sendgrid event is not supported:',
                  message_id: event_payload[:message_id], event:)

      failure!(
        reason: "Unsupported event type: #{event}",
        meta: {
          event:,
          message_id: event_payload[:message_id],
          payload: event_payload.to_json
        }
      )

      true
    end

    def missing_message_id?(event_payload)
      return false if event_payload[:message_id].present?

      # NOTE:  We previously logged + emitted a custom failure event
      #        when missing message id.  Because many systems are still
      #        using sendgrid independent of Communications Service,
      #        which triggers this status update webhook, we were seeing
      #        these ~1MM times per week.  That's a lot of extraneous
      #        logging for an expected situation, and so we're dropping
      #        the log and notification until all communication is centralized.

      true
    end
  end
end
