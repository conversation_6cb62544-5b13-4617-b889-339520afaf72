# frozen_string_literal: true

module Api
  class MessagesController < ApiController
    include LoggingConcern

    # This API endpoint is called from other services directly without an OAuth token or Identity Service
    # authorized header. These API endpoints MUST NOT BE EXPOSED PUBLICLY.
    skip_before_action :verify_auth

    def index
      messages = messages_collection

      if params[:filter].present?
        message_filter = MessageFilter.new(filters)
        return render json: { errors: message_filter.errors }, status: :bad_request unless message_filter.valid?

        messages = message_filter.call(messages)
      end

      pagination_stats, messages = pagy(messages, pagination_params)

      render json: {
        messages: ActiveModelSerializers::SerializableResource.new(messages, each_serializer: Api::MessageSerializer),
        meta: meta(pagination_stats)
      }, status: :ok
    end

    def show
      id = params.permit(:id)[:id]
      message = Message.find(id)
      render json: message, serializer: Api::MessageSerializer, status: :ok
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error("Message record not found for the following ID: #{id}", error_message: e.message,
                                                                                 message_id: id)
      render json: { errors: [{ message: e.message }] }, status: :not_found
    end

    def create
      log_action_details(method_name: __method__, custom_msg: 'Creating messsage',
                         message_params: message_params.as_json(except: %i[recipient inputs]).to_json)
      message = build_message
      return render_errors(message) unless message.valid?
      return render_limited(message) if exceeds_limit?(message)

      message.save!

      log_action_details(method_name: __method__, custom_msg: 'Message saved!', message:)

      schedule_message_delivery(message)
      render json: message, serializer: Api::MessageSerializer, status: :created
    end

    private

    def borrower_attribution_id
      message_params[:attribution]
        .find { |attribution| attribution[:type].downcase == 'borrower' }
        &.fetch(:id, nil)
    end

    def loan_attribution_id
      message_params[:attribution]
        .find { |attribution| attribution[:type].downcase == 'loan' }
        &.fetch(:id, nil)
    end

    def exceeds_limit?(message)
      return false if message.template.detail_info.limit_per_borrower.blank?
      return false if borrower_attribution_id.blank?

      template_key = message.template.detail_info.key
      message_filter = MessageFilter.new({ attribution_id: borrower_attribution_id, template_key: })
      message_filter.call(Message.all).count >= message.template.detail_info.limit_per_borrower
    end

    def messages_collection
      Message.includes(:template).order(created_at: :asc).all
    end

    def schedule_message_delivery(message)
      message_delivery_method = message.detail_info.delivery_method

      log_action_details(method_name: __method__, custom_msg: 'Scheduling message', message_delivery_method:)

      case message_delivery_method
      when Message::DetailInfo::SMS_DELIVERY_METHOD
        TextMessageScheduler.call(message:)
      when Message::DetailInfo::EMAIL_DELIVERY_METHOD
        EmailScheduler.call(message:)
      end
    end

    def pagination_params
      {
        items: index_message_params.fetch(:per_page, nil)&.to_i,
        page: index_message_params.fetch(:page, nil)&.to_i
      }
    end

    def meta(pagination_stats)
      {
        count: pagination_stats.in,
        total_count: pagination_stats.count,
        per_page: pagination_stats.items,
        current_page: pagination_stats.page,
        total_pages: pagination_stats.pages
      }
    end

    def build_message
      log_action_details(method_name: __method__, custom_msg: 'Building message',
                         template_key: message_params[:template_key])

      template = Template.find_by("detail_info->>'key' = ?", message_params[:template_key])
      template_version = template&.active_version

      new_message = Message.new(
        detail_info: build_message_detail_info,
        template:,
        template_version:
      )

      log_action_details(method_name: __method__, custom_msg: 'Message created!')

      new_message
    end

    def build_message_detail_info
      Message::DetailInfo.new(
        recipient: sanitize(message_params[:recipient]),
        delivery_method: message_params[:delivery_method],
        inputs: message_params[:inputs].to_h,
        entities: message_params[:attribution].map { |entity| Message::Entity.new(entity) }
      )
    end

    def build_errors(message)
      detail_errors = message.detail_info.errors.map { |error| "#{error.attribute}: #{error.type}" }
      message_errors = message.errors.full_messages + detail_errors
      message_errors.map { |error| { message: error } }
    end

    def render_limited(message)
      render json: {
        errors: ["Message request exceeds per-borrower limit: #{message.template.detail_info.limit_per_borrower}}"]
      }, status: :too_many_requests
    end

    def render_errors(message)
      errors = build_errors(message)
      Rails.logger.error("Invalid message creation attempt: #{errors.join(', ')}")
      render json: { errors: }, status: :unprocessable_entity
    end

    def log_action_details(method_name:, custom_msg: nil, **kwargs)
      log_details(method_name:, custom_msg:, **kwargs.merge(loan_details))
    end

    def loan_details
      { loan_id: loan_attribution_id, borrower_id: borrower_attribution_id }
    end

    def sanitize(recipient)
      case message_params[:delivery_method]
      when Message::DetailInfo::SMS_DELIVERY_METHOD
        recipient.gsub(/\D/, '')
      when Message::DetailInfo::EMAIL_DELIVERY_METHOD
        recipient.to_s.downcase
      end
    end

    def filters
      index_message_params[:filter].to_h
    end

    def index_message_params
      params.permit(
        :page,
        :per_page,
        filter: {}
      )
    end

    def message_params
      params.permit(
        :recipient,
        :delivery_method,
        :template_key,
        inputs: {},
        attribution: %i[id type]
      )
    end
  end
end
