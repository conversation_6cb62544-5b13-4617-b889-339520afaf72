# frozen_string_literal: true

module Api
  class SbtWebhooksController < ApiController
    include LoggingConcern

    def create
      log_details(method_name: __method__, custom_msg: 'Creating',
                  message_id: msp_message_id, status_code: msp_status_code)

      unless message_status_params[:Type] == 'MessageStatus'
        Rails.logger.error("Webook Status Type #{webhook_type} Not Supported")
        return head :accepted
      end

      SbtOutboundMessageStatusJob.perform_async(
        msp_message_id,
        msp_status_code,
        msp_delivered_time,
        message_status_params.to_json
      )

      head :accepted
    end

    private

    def webhook_type
      message_status_params[:Type]
    end

    def msp_delivered_time
      message_status_params.dig(:Payload, :DeliveredTime)
    end

    def msp_status_code
      message_status_params.dig(:Payload, :StatusCode)
    end

    def msp_message_id
      message_status_params.dig(:Payload, :MessageId)
    end

    def message_status_params
      params.permit(
        :Type,
        Payload: %i[
          AccountId
          Message
          Msisdn
          GroupName
          GroupId
          CommunicationCode
          DeliveredTime
          Properties
          StatusCode
          StatusCodeDescription
          MessageId
          ReferenceId
          TotalMessageSegments
          MessageType
        ]
      )
    end
  end
end
