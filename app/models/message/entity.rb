# frozen_string_literal: true

class Message
  class Entity
    include StoreModel::Model

    # entity type explanations:
    # APPLICATION: [DEPRECATED] Loan ID (IPL loans) or LOAN INQUIRY ID (UPL loans)
    # BORROWER: Borrower ID
    # DECISION
    # LOAN: Loan ID
    # LOAN_INQUIRY: LoanInquiry ID
    # SOURCE: Source system identifier (e.g., "AMS" or "DASH")
    ENTITY_TYPES = %w[APPLICATION BORROWER DECISION LOAN LOAN_INQUIRY SOURCE].freeze

    attribute :id, :string
    enum :type, in: ENTITY_TYPES.index_by(&:to_sym)
  end
end
