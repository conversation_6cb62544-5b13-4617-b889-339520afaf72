# frozen_string_literal: true

class Message
  class DetailInfo
    include StoreModel::Model

    DELIVERY_METHODS = [
      SMS_DELIVERY_METHOD = 'SMS',
      EMAIL_DELIVERY_METHOD = 'EMAIL'
    ].freeze

    STATUSES = [
      PENDING_STATUS = 'PENDING',
      TRIGGERED_STATUS = 'TRIGGERED',
      DELIVERED_STATUS = 'DELIVERED',
      FAILED_STATUS = 'FAILED',
      UNSUBSCRIBED_STATUS = 'UNSUBSCRIBED',
      UNDELIVERABLE_STATUS = 'UNDELIVERABLE',
      UNKNOWN_STATUS = 'UNKNOWN',
      UNSCHEDULABLE_STATUS = 'UNSCHEDULABLE'
    ].freeze

    attribute :inputs
    attribute :recipient, :string
    attribute :schedule_at, :datetime

    enum :status, in: STATUSES.index_by(&:to_sym), default: PENDING_STATUS
    enum :delivery_method, in: DELIVERY_METHODS.index_by(&:to_sym)

    validates :recipient, :delivery_method, presence: true
    validate :validate_phone_number, if: -> { SMS? }

    attribute :entities, Message::Entity.to_array_type, array: true, default: []
    validates :entities, store_model: true

    private

    def validate_phone_number
      # TODO: SBT requires a leading 1 country code so this regex enforces that
      return if recipient =~ /\A(\+\d{1,2}\s?)?1[-. ]?\(?\d{3}\)?[-. ]?\d{3}[-. ]?\d{4}\z/

      errors.add(:recipient, 'must be a valid telephone number')
    end
  end
end
