# frozen_string_literal: true

class Message < ApplicationRecord
  has_paper_trail

  belongs_to :delivery, polymorphic: true, optional: true
  belongs_to :template, required: true
  belongs_to :template_version, required: true

  attribute :detail_info, Message::DetailInfo.to_type, default: Message::DetailInfo.new

  validate :valid_for_template_version
  validates_associated :detail_info

  def associated_loan_id
    detail_info&.entities&.find { |entity| entity.type&.downcase == 'loan' }&.id
  end

  private

  def input_for(key)
    detail_info.inputs.fetch(key.to_s, nil)
  end

  def valid_for_template_version
    return if template_version.blank?

    template_version.template_variables.each do |variable|
      input = input_for(variable.key)
      errors.add("inputs - #{variable.key}", variable.error) unless variable.valid_for?(input)
    end
  end
end
