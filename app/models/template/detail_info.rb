# frozen_string_literal: true

class Template
  class DetailInfo
    include StoreModel::Model

    TOPICS = [
      ORIGINATIONS_TOPIC = 'ORIGINATIONS',
      SERVICING_TOPIC = 'SERVICING',
      DELINQUENCY_TOPIC = 'DELINQUENCY',
      CUSTOMER_CARE_TOPIC = 'CUSTOMER_CARE'
    ].freeze

    attribute :key, :string
    attribute :name, :string
    attribute :limit_per_borrower, :integer

    # Boolean flag indicating messages that should ignore unsubscribe and/or suppression lists (i.e. critical
    # transactional communications like password reset emails, required regulatory disclosures, etc).
    attribute :override_suppression, :boolean

    # Boolean flag indicating messages that should attach an above lending VCF card to the email.
    attribute :attach_vcf_card, :boolean

    enum :topic, in: TOPICS.index_by(&:to_sym)

    validates :key, :name, :topic, presence: true
  end
end
