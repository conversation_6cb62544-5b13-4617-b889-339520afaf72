# frozen_string_literal: true

class TemplateVersion
  class DetailInfo
    include StoreModel::Model

    STATUSES = [
      ACTIVE_STATUS = 'ACTIVE',
      ARCHIVED_STATUS = 'ARCHIVED',
      DRAFT_STATUS = 'DRAFT',
      PAUSED_STATUS = 'PAUSED'
    ].freeze

    attribute :content, :string
    attribute :vendor_id, :string
    attribute :variables, array: true, default: []

    enum :status, in: STATUSES.index_by(&:to_sym)

    validates_inclusion_of :status, in: STATUSES
    validate :only_supported_variables

    private

    def only_supported_variables
      unsupported_variables = variables - TemplateVariables::VARIABLES.keys.map(&:to_s)
      return if unsupported_variables.blank?

      errors.add(:variables, "The following are unsupported: #{unsupported_variables}")
    end
  end
end
