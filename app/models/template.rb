# frozen_string_literal: true

class Template < ApplicationRecord
  has_paper_trail

  has_many :messages
  has_many :template_versions

  attribute :detail_info, Template::DetailInfo.to_type, default: Template::DetailInfo.new

  validates_associated :detail_info

  def active_version
    status = TemplateVersion::DetailInfo::ACTIVE_STATUS
    template_versions.find_by("detail_info->>'status' = :status", { status: })
  end
end
