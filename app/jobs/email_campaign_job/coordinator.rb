# frozen_string_literal: true

module EmailCampaignJob
  class Coordinator < ApplicationJob
    include EmailCampaign::Helpers

    WORKER_ENQUEUE_INITIAL_DELAY = 5.minutes
    WORKER_ENQUEUE_INTERVAL = 1.minute

    def perform(campaign_name, template_name, delivery_rate = DEFAULT_DELIVERY_RATE)
      email_count = parsed_contacts_file(campaign_name).count

      worker_count = (email_count / delivery_rate.to_f).ceil

      worker_count.times do |worker_index|
        EmailCampaignJob::Worker
          .set(wait: WORKER_ENQUEUE_INITIAL_DELAY + (WORKER_ENQUEUE_INTERVAL * worker_index))
          .perform_async(worker_index, campaign_name, template_name, delivery_rate)
      end
    end
  end
end
