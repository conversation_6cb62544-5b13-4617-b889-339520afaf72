# frozen_string_literal: true

module EmailCampaignJob
  class SendEmail < ApplicationJob
    attr_reader :message_data, :template_name

    def perform(message_data, template_name)
      @message_data = message_data
      @template_name = template_name

      SendgridApi.send_message!(personalizations:, sg_template_id: template_version.detail_info.vendor_id, template:)
    end

    private

    def personalizations
      message_data.map do |message_attr|
        SendgridApi.build_personalization(
          email: message_attr['recipient'],
          template_data: {},
          message_id: message_attr['id']
        )
      end
    end

    def template
      @template ||= Template.includes(:template_versions).find_by("detail_info->>'key' = ?", template_name)
    end

    def template_version
      @template_version ||= template.active_version
    end
  end
end
