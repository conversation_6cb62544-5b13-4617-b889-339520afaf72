# frozen_string_literal: true

module EmailCampaignJob
  class Worker < ApplicationJob
    include EmailCampaign::Helpers

    attr_reader :campaign_name, :template_name, :worker_index, :delivery_rate

    def perform(worker_index, campaign_name, template_name, delivery_rate)
      Rails.logger.info("Parsing contacts file with campaign_name: #{campaign_name}, " \
                        "template_name: #{template_name}, worker_index: #{worker_index}")
      @campaign_name = campaign_name
      @template_name = template_name
      @worker_index = worker_index
      @delivery_rate = delivery_rate

      validate!
      process
    rescue StandardError => e
      Rails.logger.error("Error during processing of worker #{worker_index} with campaign_name: #{campaign_name}, " \
                         "template_name: #{template_name}, error: #{e.message}")
    end

    private

    def validate!
      if contacts.empty?
        raise "No contact emails found for campaign_name: #{campaign_name}"
      elsif template.blank?
        raise "Template with name #{template_name} not found"
      end
    end

    def process
      ActiveRecord::Base.transaction do
        messages = build_messages

        create_message_records(messages)
        create_delivery_records(messages)
        bulk_send_messages(messages)
      end
    end

    def contacts
      # worker is only responsible for processing the `worker_index` chunk of CSV
      @contacts ||= parsed_contacts_file(campaign_name).drop(worker_index * delivery_rate).take(delivery_rate)
    end

    def template
      @template ||= Template.includes(:template_versions).find_by("detail_info->>'key' = ?", template_name)
    end

    def template_version
      @template_version ||= template.active_version
    end

    def build_messages
      contacts.map do |contact|
        Message.new(
          id: SecureRandom.uuid,
          detail_info: build_message_detail_info(contact),
          template:,
          delivery: Email.new(id: SecureRandom.uuid),
          template_version:,
          created_at: Time.current,
          updated_at: Time.current
        )
      end
    end

    def build_message_detail_info(contact)
      application_entity = Message::Entity.new({ id: campaign_name, type: :APPLICATION })
      loan_entity = Message::Entity.new({ id: contact[CSV_LOAN_ID_COLUMN_NAME], type: :LOAN })

      Message::DetailInfo.new(
        recipient: contact[CSV_EMAIL_COLUMN_NAME].downcase,
        delivery_method: Message::DetailInfo::EMAIL_DELIVERY_METHOD,
        inputs: {},
        entities: [application_entity, loan_entity],
        status: Message::DetailInfo::TRIGGERED_STATUS
      )
    end

    def create_message_records(messages)
      Message.insert_all(messages.map(&:attributes), returning: false)
    end

    def create_delivery_records(messages)
      deliveries = build_deliveries(messages)
      Email.insert_all(deliveries.map(&:attributes), returning: false)
    end

    def build_deliveries(messages)
      messages.map do |message|
        Email.new(id: message.delivery_id, created_at: message.created_at)
      end
    end

    def bulk_send_messages(messages)
      messages_payload = build_messages_payload(messages)
      EmailCampaignJob::SendEmail.perform_async(messages_payload, template_name)
    end

    def build_messages_payload(messages)
      messages.map do |message|
        { 'id' => message.id, 'recipient' => message.detail_info.recipient }
      end
    end
  end
end
