# frozen_string_literal: true

class SbtOutboundMessageStatusJob < ApplicationJob
  include ObservabilityEvent
  include LoggingConcern

  DELIVERED_STATUS_CODES = ['100'].freeze
  TRIGGERED_STATUS_CODES = %w[101 102].freeze
  FAILED_STATUS_CODES = %w[200 404 408 409 410 411 412 572 800 9999].freeze

  # rubocop:disable Metrics/MethodLength
  def perform(vendor_message_id, status_code, timestamp, payload)
    log_details(method_name: __method__, custom_msg: 'SbtOutboundMessageStatusJob job started',
                vendor_message_id:, status_code:, timestamp:)
    text_message = TextMessage.find_by(vendor_message_id:)

    unless text_message
      success!(vendor_message_id:, ignored: true)
      return
    end

    event_occurred_at = DateTime.parse(timestamp)
    message = text_message.message
    message_status = message_status_from(status_code)

    TextMessageEvent.transaction do
      log_details(method_name: __method__, custom_msg: 'creating TextMessageEvent', message:, vendor_message_id:)
      TextMessageEvent.create!(text_message:, status_code:, payload: JSON.parse(payload), created_at: event_occurred_at)
      update_message_status(message, message_status, event_occurred_at)
    end

    case message_status
    when Message::DetailInfo::FAILED_STATUS, Message::DetailInfo::UNKNOWN_STATUS
      failure!(reason: message_status, meta: { message_id: message.id })
    else
      log_details(method_name: __method__, custom_msg: 'Job Complete', message:, vendor_message_id:)
      success!(message_id: message.id)
    end
  end
  # rubocop:enable Metrics/MethodLength

  private

  def update_message_status(message, status, event_occurred_at)
    log_details(method_name: __method__,
                message:,
                status:, event_occurred_at:)

    # We're proactively doing this for SBT because we observed SendGrid sending us
    # events out of order, which led to the status of a message not being updated
    # properly based on the webhook payload we receive.
    return if TextMessageEvent.where(TextMessageEvent.arel_table[:created_at].gt(event_occurred_at)).exists?

    message.detail_info.status = status
    message.save!
  end

  def message_status_from(solutions_by_text_code)
    case solutions_by_text_code
    when *DELIVERED_STATUS_CODES
      Message::DetailInfo::DELIVERED_STATUS
    when *TRIGGERED_STATUS_CODES
      Message::DetailInfo::TRIGGERED_STATUS
    when *FAILED_STATUS_CODES
      Message::DetailInfo::FAILED_STATUS
    else
      Message::DetailInfo::UNKNOWN_STATUS
    end
  end
end
