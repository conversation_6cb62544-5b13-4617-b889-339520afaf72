# frozen_string_literal: true

class SendgridEmailDeliveryEventJob < ApplicationJob
  include ObservabilityEvent
  include LoggingConcern

  # https://docs.sendgrid.com/for-developers/tracking-events/event
  SENDGRID_DELIVERY_EVENT_TO_STATUS = {
    'processed' => Message::DetailInfo::TRIGGERED_STATUS,
    'deferred' => Message::DetailInfo::UNDELIVERABLE_STATUS,
    'dropped' => Message::DetailInfo::UNSUBSCRIBED_STATUS,
    'bounce' => Message::DetailInfo::FAILED_STATUS,
    'delivered' => Message::DetailInfo::DELIVERED_STATUS
  }.freeze

  ENGAGEMENT_EVENTS = %w[
    open
    click
  ].freeze

  SUPPORTED_EVENT_TYPES = (ENGAGEMENT_EVENTS + SENDGRID_DELIVERY_EVENT_TO_STATUS.keys).freeze

  TERMINAL_MESSAGE_STATUSES = [
    Message::DetailInfo::FAILED_STATUS,
    Message::DetailInfo::UNSUBSCRIBED_STATUS,
    Message::DetailInfo::DELIVERED_STATUS
  ].freeze

  MESSAGE_STATUS_PRIORITY = [
    Message::DetailInfo::UNSCHEDULABLE_STATUS,
    Message::DetailInfo::PENDING_STATUS,
    Message::DetailInfo::TRIGGERED_STATUS,
    Message::DetailInfo::UNDELIVERABLE_STATUS,
    Message::DetailInfo::UNKNOWN_STATUS
  ] + TERMINAL_MESSAGE_STATUSES

  def perform(event_type, message_id, timestamp, payload)
    log_details(method_name: __method__, custom_msg: 'SendgridEmailDeliveryEventJob started',
                event_type:, message_id:)

    event_occurred_at = Time.at(timestamp).to_datetime
    message = Message.find_by(id: message_id)

    unless message
      success!(message_id:, ignored: true)
      return
    end

    record_email_event(message, event_type, event_occurred_at, payload)

    # don't update the message status on engagement events (open, click)
    return if ENGAGEMENT_EVENTS.include?(event_type)

    update_message_status(message, event_type)

    case message.detail_info.status
    when Message::DetailInfo::FAILED_STATUS, Message::DetailInfo::UNKNOWN_STATUS
      failure!(reason: message.detail_info.status, meta: { message_id: })
    else
      success!(message_id:)
    end
  end

  private

  def record_email_event(message, event, event_occurred_at, payload)
    EmailEvent.create!(email: message.delivery, event:, payload: JSON.parse(payload), created_at: event_occurred_at)
  end

  def update_message_status(message, event_type)
    current_message_status = message.detail_info.status
    new_message_status = SENDGRID_DELIVERY_EVENT_TO_STATUS[event_type.downcase] || Message::DetailInfo::UNKNOWN_STATUS

    if !TERMINAL_MESSAGE_STATUSES.include?(current_message_status) &&
       MESSAGE_STATUS_PRIORITY.index(new_message_status) >= MESSAGE_STATUS_PRIORITY.index(current_message_status)

      log_details(method_name: __method__, message:, event_type:, new_message_status:, current_message_status:)

      message.detail_info.status = new_message_status
      message.save!
    else
      log_details(method_name: __method__, custom_msg: 'Skipping message status updating',
                  message:, event_type:, new_message_status:, current_message_status:)
    end
  end
end
