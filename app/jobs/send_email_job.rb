# frozen_string_literal: true

class SendEmailJob < ApplicationJob
  include ObservabilityEvent
  include LoggingConcern

  sidekiq_options retry: 5

  def perform(message_id)
    log_details(method_name: __method__, custom_msg: 'SendEmailJob job started', message_id:)

    message = Message.find(message_id)
    recipient = message.detail_info.recipient

    # If the recipient has unsubscribed, sendgrid will suppress the message and emit a `dropped` webhook event.
    # Communications service uses this event to set the Message status to UNSUBSCRIBED.
    trigger_email!(message, recipient)
    record_delivery!(message)

    log_details(method_name: __method__, custom_msg: 'Job complete - email sent', message:)
    success!(message_id:)
  end

  private

  def trigger_email!(message, recipient)
    sg_template_id = message.template_version.detail_info.vendor_id
    template_data = AttributeFormatter.call(message.detail_info.inputs)
    template = message.template_version.template

    log_details(method_name: __method__, custom_msg: 'Sending', message:, sg_template_id:)

    attachments = DownloadAttachments.call(message.detail_info.inputs) do |content, filename, type|
      SendgridApi.build_attachment(content:, filename:, type:)
    end

    personalizations = [
      SendgridApi.build_personalization(email: recipient, template_data:, message_id: message.id)
    ]

    SendgridApi.send_message!(personalizations:, sg_template_id:, template:, attachments:)
  end

  def record_delivery!(message)
    Email.transaction do
      delivery = Email.create!
      message.delivery = delivery
      message.detail_info.status = Message::DetailInfo::TRIGGERED_STATUS
      message.save!
    end
  end
end
