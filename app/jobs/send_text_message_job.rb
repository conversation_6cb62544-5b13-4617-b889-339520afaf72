# frozen_string_literal: true

class SendTextMessageJob < ApplicationJob
  include ObservabilityEvent
  include LoggingConcern

  WHITELISTED_PHONE_NO = %w[16302788917 16302788916 16302788915 12248131163 12248131199 12248131212 12245251351
                            12245251352 12245251353 12134001782 17325399094].freeze

  def perform(message_id)
    message = Message.find(message_id)

    log_details(method_name: __method__, custom_msg: 'SendTextMessageJob started', message:)

    return unschedulable(message) unless TextMessageScheduler.within_schedule?(message)

    phone_number = message.detail_info.recipient

    unless number_is_whitelisted(phone_number)
      Rails.logger.warn("Phone number #{phone_number} is not whitelisted for message_id: #{message_id}")
      return
    end
    return unless ensure_subscription(message)

    vendor_message_id = SbtApi.send_message!(phone_number, text_message_body(message))
    record_delivery!(message, vendor_message_id)

    log_details(method_name: __method__, custom_msg: 'Job complete - message sent', message:, vendor_message_id:)
    success!(message_id:)
  end

  private

  def number_is_whitelisted(phone_number)
    return true if Rails.env.production?

    sanitized_phone_number = phone_number.delete(' -')
    WHITELISTED_PHONE_NO.include?(sanitized_phone_number)
  end

  def ensure_subscription(message)
    phone_number = message.detail_info.recipient
    subscriber_status = SbtApi.get_status(phone_number).dig('data', 0, 'status')

    log_details(method_name: __method__, custom_msg: 'Checking subscriber status.',
                message:, subscriber_status:)

    case subscriber_status
    when SbtApi::INACTIVE_SUBSCRIBER_STATUS
      unsubscribed(message)
      false
    when SbtApi::ACTIVE_SUBSCRIBER_STATUS
      true
    else
      unless SbtApi.valid_phone_number?(phone_number)
        landline(message)
        return false
      end

      SbtApi.add_subscriber!(phone_number)
      true
    end
  end

  def unschedulable(message)
    message.detail_info.status = Message::DetailInfo::UNSCHEDULABLE_STATUS
    message.save!

    log_details(method_name: __method__, custom_msg: 'Unable to schedule', message:)

    failure!(reason: 'unschedulable', meta: { message_id: message.id })
  end

  def text_message_body(message)
    formatted_variables = AttributeFormatter.call(message.detail_info.inputs)
    content = message.template_version.detail_info.content

    Liquid::Template.parse(content).render(formatted_variables)
  end

  def unsubscribed(message)
    message.detail_info.status = Message::DetailInfo::UNSUBSCRIBED_STATUS
    message.save!

    log_details(method_name: __method__, custom_msg: 'Unsubscribed user.', message:)

    failure!(reason: 'unsubscribed', meta: { message_id: message.id })
  end

  def landline(message)
    message.detail_info.status = Message::DetailInfo::UNDELIVERABLE_STATUS
    message.save!

    log_details(method_name: __method__, custom_msg: 'Number is a landline.', message:)

    failure!(reason: 'landline', meta: { message_id: message.id })
  end

  def record_delivery!(message, vendor_message_id)
    TextMessage.transaction do
      delivery = TextMessage.create(vendor_message_id:)
      message.delivery = delivery
      message.detail_info.status = Message::DetailInfo::TRIGGERED_STATUS
      message.save!
    end
  end
end
