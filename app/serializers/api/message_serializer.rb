# frozen_string_literal: true

module Api
  class MessageSerializer < ActiveModel::Serializer
    attributes :id, :recipient, :delivery_method, :template_key, :inputs, :attribution, :status, :created_at,
               :updated_at

    delegate :recipient, :delivery_method, :template_key, :inputs, :status, to: :detail_info

    def detail_info
      object.detail_info
    end

    def template_key
      object.template.detail_info.key
    end

    def attribution
      object.detail_info.entities
    end
  end
end
