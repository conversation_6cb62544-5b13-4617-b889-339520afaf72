FROM ruby:3.3.7-bullseye

ARG RAILS_ENV
ARG BUNDLE_GEMS__CONTRIBSYS__COM

# Datadog's source code integration 
ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL} 
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}

RUN sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt bullseye-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
RUN wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -

RUN apt -y update && apt install -y binutils curl git gnupg cmake python-is-python3 python-dev-is-python3 \
  postgresql-client-12 supervisor tar tzdata apt-transport-https apt-utils \
  libpq-dev


RUN mkdir /rails_terraform_docker
COPY . /rails_terraform_docker
WORKDIR /rails_terraform_docker

COPY Gemfile.lock ./
ENV BUNDLE_PATH=/usr/local/bundle
RUN gem install bundler:$(cat Gemfile.lock | tail -1 | tr -d " ") && \
  bundle config --local deployment 'true' && \
  bundle config --local force_ruby_platform 'true' && \
  bundle config gems.contribsys.com $BUNDLE_GEMS__CONTRIBSYS__COM && \
  (bundle check || bundle install -j$((`nproc`-1)))

RUN chmod a+x bin/rails bin/rake bin/bundle
# Set for the PGHero UI and assets
# NOTE:  Sets environment variables that are expected to exist when rails executes
#        but which are not otherwise necessary for asset precompilation.
#        SECRET_KEY_BASE is set to a non-empty value to avoid precompilation failure.
RUN SECRET_KEY_BASE_DUMMY=1 \
  SECRET_KEY_BASE='SECRET_KEY_BASE' \
  DEVISE_SECRET_KEY='DEVISE_SECRET_KEY' \
  FLIPPER_UI_SECRET='' \
  FLIPPER_SLACK_HOOK='' \
  RAILS_ENV=development \
  bin/rails assets:precompile

# Serve PGHero UI assets
ENV RAILS_SERVE_STATIC_FILES=true

# Set the environment variable
ARG BUILD_TYPE=prod

# Sets development environment specific instructions
RUN if [ "$BUILD_TYPE" = "prod" ]; then \
        echo "Skipping GraphViz for Production"; \
    else \
        echo "Building for development" && \
        apt-get install -y python3-pydot graphviz && \
        bundle config unset deployment; \
    fi
COPY docker/entrypoint.sh docker/entrypoint.sh

RUN chmod +x docker/entrypoint.sh
ENTRYPOINT [ "docker/entrypoint.sh" ]

# Start the main process.
CMD ["start-web"]

EXPOSE 3002
