# Communications Service

The Communications Service serves as a central point of integration with email, text message, and other third-party communications providers.  It provides a single interface for other technical systems within Above's service architecture to send various messages to customers, while maintaining a consistent audit trail, respecting allowed messaging windows, and enforcing correctness with regard to the messages that it transmits.

Further information about the Communications Service may be found:
  - In Slack at [#comms-system](https://abovelending.slack.com/messages/C056FNRSDNF) or [#comms-system-alerts](https://abovelending.slack.com/messages/C05G3L9AFCJ)
  - In Confluence in the [Communications Service](https://abovelending.atlassian.net/wiki/spaces/PROD/pages/**********/Communications+Service) space
  - In Jira in the [Communications System](https://abovelending.atlassian.net/jira/software/projects/CS/boards/34) project
  - In Datadog on the [Communications Service](https://app.datadoghq.com/dashboard/xqx-ghc-a7r/communications-service) dashboard

## Responsibilities

The Communications Service has three primary responsibilities:
  - Acting as the single integration interface from Above to our third-party communications providers
  - Maintaining an audit trail of the communications that have been sent to our customers
  - Enforcing correctness in the messages that it transmits

Its secondary responsibilities include maintaining "allowed" messaging windows (e.g., to send text messages only between the hours of 11am - 6pm CT), and exposing a basic REST API for other services to retrieve data about messages that have been sent.

It currently **does not** enforce non-delivery to opted-out or unsubscribed customers (generally delegating this responsibility to the corresponding third-party communications provider), nor does it enforce correctness of templates that are stored _outside_ of the Service itself (e.g., those email templates that are housed in SendGrid.)

### Single Integration Interface

From follow-up emails during our origination process, to payment text messages while in loan servicing, many of Above's technical systems benefit from communicating with our customers.  Rather than have each of our technical systems execute these integrations individually, Communications Service exposes a single `messages#create` interface which allows those services a simple and unified way to execute those necessary communications.

The Communications Service currently integrations with [Solutions By Text](https://solutionsbytext.com/) for SMS communication, and with [SendGrid](https://sendgrid.com/) for email communication.

### Audit Trail

As a business operating in the finance and lending space, it's deeply important that Above be able to confidently and correctly maintain a log of all communications that have been sent to customers.  The Communications Service maintains this audit trail in a few tables within its relational database:
  - The `messages` table stores primary records that correspond to each communication that we've sent to borrowers, including the delivery method (email or sms), message inputs (e.g., the borrower's first name, payment date, and payment amount which were interpolated into a welcome email), and attribution to the corresponding entities in Above's other systems (the associated loan and/or borrower records)
  - The `text_messages` and `text_message_events` tables, which store records that correspond to the specific execution of an SMS/text message request that was made to Solutions By Text.  While executing the actual delivery of a message, Solutions By Text emits a number of "status updates" via webhook which are store in the "events" table and track the various steps of delivery or delivery failure for that message.
  - The `emails` and `email_events` tables, which store records that correspond to the specific execution of an email request that was made to SendGrid.  While executing the actual delivery of a message, SendGrid emits a number of "status updates" via webhook which are store in the "events" table and track the various steps of delivery or delivery failure for that message.

### Message Correctness

It does not serve our customers -- nor does it make Above look very good -- to delivery messages that are malformed.  The most common cause of a malformed message is an expectation to receive a given variable which should be interpolated into the corresponding template, but which is not sent.  This usually results in the template's variable interpolation syntax remaining included in the literal message sent to a customer:  the `Hey, {{important_customer_first_name}}, you're an important part of our business!`.

In order to prevent malformed messages the Communications Service enforces, to the best of its ability, that all communication requests made by Above's systems include all required variables that a given template requires.  In order to execute this validation, the Communications Service must maintain references to the templates and their variable requirements for both text messages and emails.  These references are maintained manually -- generally via seeding the `templates` and `template_versions` tables via the `db/seeds.rb` script.  The operational processes for updating templates are described in [Template Updates](#template-updates).

## Template Updates

Changes to Text Messages or Email templates generally start with a request from the business.  This process is outlined in Confluence as a set of change controls:
  - [SMS Template Management Change Control](https://abovelending.atlassian.net/wiki/spaces/PROD/pages/1869152309/SMS+Template+Management+Change+Control)
  - [Email Template Management Change Control](https://abovelending.atlassian.net/wiki/spaces/PROD/pages/1876066305/Email+Template+Management+Change+Control)

Template records in our deployed sandbox, staging, and production environments are managed via the standard Rails DB seeding processing. These seeds are idempotent and triggered as part of each deployment to these environments.

Updates to templates that add or change variables (or requests for an entirely new template) require that engineering work be completed **first** such that the system responsible for triggering the corresponding templated message has been updated to _send_ the new variables as inputs.  The Communications Service will accept extra variables, but will reject requests that do not include the full set of required variables.

When making updates that add or change variables, we must introduce a new `template_version` record which includes the relevant changes (including the copy in the `content` field for SMS templates, or the `vendor_id` that references a SendGrid email template.)  Only the "active" template version is used, and there must be only one active template version at a given time.

Care must be taken to deactivate the old template version and activate the new one in concert with ensuring that messages that use that template already include new variables -- and that the product/marketing/operations group is ready for the change.

## Setup

AboveLending employs Docker for its deployed environments, providing flexibility for development setups. You have two options:

* Run Locally: Use your local machine's environment for development.
* Use Docker: Leverage our Docker-based setup, known as ALP (Above Lending Platform), for a consistent and containerized development experience.

## Recommendation

We recommend using the ALP Docker setup for the following reasons:

* Matches the deployed environment, ensuring compatibility.
* Simplifies dependency management and configuration, for seamless development in lower environments.
* Provides a unified setup across different development teams.

## ALP V2 Setup

ALP V2 simplifies the development environment by consolidating services into a centralized repository, alp-development. This version introduces improved dependency management, streamlined environment variable handling, and new Makefile commands for enhanced usability.

### Prerequisites

Setup alp-development directions to install Docker, Bitwarden CLI, and setup environment variables.  Follow [ALP-Development Prerequisites](https://github.com/Above-Lending/alp-development/tree/main?tab=readme-ov-file#prerequisites).

In alp-development, verify ALP's healthcheck is valid by running `make doctor`.

### Setup Instructions:
1. Setup alp-development by following it's readme:
  [ALP-Development Prerequisites](https://github.com/Above-Lending/alp-development/tree/main?tab=readme-ov-file#prerequisites)
  - Start services (Postgres, redis, minio)
    ```
    make start
    ```
1. Setup Communications Service
  - In application_management_system run
    ```
    make alp.upgrade
    make alp.setup
    make alp.secrets.pull
    ```

### Usage

ALP is a fancy wrapper on docker, so docker commands can be called directly on the `alp` service. In addition, the Communications Service compose file includes a `sidekiq` service to create a first class development environment. A Makefile provides the core functions and calling `make` will print help instructions.

#### Suggested Aliases
Add these aliases to your configuration file (~/.zshrc, ~/.bash_profile, ~/.profile) for easier convenience when working inside a docker container.

```
dc='docker compose'
dcr='dc run --rm'
dcrc='dcr alp command'
dcu='dc up'
```

Example usage:
* `dcu alp` starts the *alp* service in each application
* `dcrc bash` starts the *alp* docker container with a bash interface
* `dcrc bundle` starts the *alp* docker container and runs bundle install
* `dcu up -d && docker attach communications-service-alp-1` This starts all docker compose services and attaches to the communications service container for interacting with the debugger

### Makefile:

Makefile contains core commands to our workflow and simplifies the longer commands like the `docker attach application_managementsystem-alp-1` command above.  Calling `make start` will start all services and attach to a running rails server that will allow `binding.pry` debugging.

| Make Command | Description |
| -------- | ------- |
| make alp.setup | Sets up the ALP platform. |
| make alp.upgrade | Upgrades to ALP V2. |
| make alp.downgrade | Downgrades to ALP V1. |
| make alp.secrets.pull | Fetches environment files from Bitwarden. |
| make alp.secrets.push	| Pushes environment files to Bitwarden. |
| make bash	| Opens a bash prompt on a running container. |
| make start | Starts all ALP servicess. |
| make stop	| Stops all ALP services. |
| make exec.bash | Opens a bash prompt on a running container. |

### ALP Secret Management

ALP uses two sets of secrets: **default rails secrets** and **ALP Environment Variables**. ALP Environment Variables are stored securely in Bitwarden. To manage these, use the provided make commands to Pull or Push secrets from/to Bitwarden.

When updating secrets, **ALWAYS** follow these steps:

1. Pull the latest version using make alp.secrets.pull.
1. Modify the relevant .alp/.env.[environment] file as needed.
1. Push the updated secrets back to Bitwarden using make alp.secrets.push.

#### Default Environment

By default, docker commands will use the DEFAULT environment. The terminal prompt will indicate this with:

```bash
ALP [DEFAULT] /rails_terraform_docker #
```

In this setup, you can run Rails commands (except the server) inside the Docker container. For example, run:

```bash
make bash
```

or

```bash
dcrc bash
```

to open a bash session in the DEFAULT environment.

#### Changing ALP Environments

To switch to a different environment (e.g., Development, Sandbox, or Staging), source the `bin/setenv [environment]` script before running any commands. For example:

```bash
source bin/setenv development
```
This will start a new session with the specified environment and update the terminal prompt accordingly:

```bash
Starting a new shell with:
  COMPOSE_FILE=compose.yml:compose.development.yml
  PS1=ALP [SETENV: development] $

Type 'exit' to leave the development environment.

ALP [SETENV: development] $
```
The prompt clearly indicates that you are running `ALP` in the `SETENV` session for `development` environment.

#### Working in the Environment

With the `setenv` environment prepared, you can run any Docker command in that context. For example:

```bash
ALP [SETENV: development] $ dcrc bash
Running command bash
ALP [development] /rails_terraform_docker #
```
The updated prompt confirms:

* You are working in the `ALP` platform.
* The active environment is `development`.
* You are inside the Docker environment (/rails_terraform_docker).

#### ALP Configuration File

The `.alp/.env.configuration` file simplifies controlling how ALP operates. For example, it allows you to test a Sidekiq job from the sandbox environment while using your local Redis instance to test code changes, all while defaulting to GDS and the sandbox database as the environment is normally configured.

Some options include:
* Local Web Hook Handling: Route and test webhooks locally.
* Local Cache Store: Use a local cache store for development purposes.
* Local Sidekiq Redis: Connect Sidekiq to a local Redis instance.
* Disable Sidekiq inline: Prevent Sidekiq jobs from running inline when running.

#### Important Notes
* Stop all services when switching to a different environment
* Be cautious when working in non-development environments (e.g., Staging). Running migrations in Staging is generally not advisable.
* If you make changes to environment variables, restart the session by exiting (exit) and re-running the Docker setup commands.
* By following these guidelines, you can effectively manage and switch between ALP environments for seamless development and testing.

### Local Setup
Local setup can be setup, but not advised as the ALP docker setup provides a more streamline development environment.

### Prerequisites

This project requires:

* Ruby 3.3.7, preferably managed using [rbenv][https://github.com/rbenv/rbenv]
* [Docker Desktop](https://www.docker.com/products/docker-desktop/) **OR** [asdf](https://asdf-vm.com/) and [Redis](https://redis.io/docs/getting-started/installation/install-redis-on-mac-os/) (See [Setup](#setup), below)

### Install local Version

To setup a new development environment for this application:

1. Install the Ruby version specified in the `Gemfile`.

1. Copy development environment variables and fill in any required secret values from AWS Secrets Manager or by asking a friend.
    * > cp .env.example .env.development

1. Setup your infrastructure

**With ASDF/Redis (Brew)**

If you prefer, you can use the [asdf package manager](https://asdf-vm.com/) and [homebrew](https://brew.sh/) to run infrastructure on your local machine.

> asdf plugin-add ruby; asdf plugin-add postgres; asdf install
>
> brew install redis

*Note*: You'll need to add an communications service postgres user with schema access:

> CREATE ROLE communications_service WITH LOGIN SUPERUSER PASSWORD 'password';

1. Run application setup:
    ```
    bin/setup
    ```

### Docker Setup

**Above Lending Platform (ALP) Docker Setup**
1. Copy development environment variables and fill in any required secret values from AWS Secrets Manager or by asking a friend.
    * > cp .env.example .env.development

1. Create .env.alp.override file
    * > touch .env.alp.override

1. You will need to set `BUNDLE_GEMS__CONTRIBSYS__COM` as well. You can get the value for it from BitWarden. Then, add it to your shell config as well:
`export BUNDLE_GEMS__CONTRIBSYS__COM=value-from-bitwarden`

1. Run setup script:
    * > docker compose run --rm alp command bin/setup
2. Start the alp web app container
    * > docker compose up alp

**Using ALP docker in each repository:**
**NOTE:** Once started the web server will be accessible at http://localhost:3003/

Docker is setup with the same docker image used in our deployed environments

    * docker compose up alp: start rails server
    * docker compose exec alp bash executes the bash command inside a running Docker Container
    * docker compose run --rm alp command echo "hello": Starts a container runs the entrypoint.sh command with the echo "hello" command and removes the container.
    * docker compose run --rm alp command bundle install: Runs bundle install
    * docker compose run --rm alp command bash: Opens a docker container running bash

**Environment Variables**
By default the dotenv files will be used as a normal rails instance, these can
be over written by `.env.alp.override` file.

    * .env.alp.override: Custom environment variables are stored here

**Suggested Aliases**

    * dc='docker compose'
    * dcr='dc run --rm'
    * dcrc='dcr alp command'

### Local Development Environment

Once your local environment is setup, you can use the following commands to run a development environment directly on your machine:

* To run the web server for this application:
    ```
    bin/rails s
    ```
    * Once started the web server will be accessible at http://localhost:3000/
    * The Sidekiq dashboard is available at http://localhost:3000/sidekiq
* To run Sidekiq and [process background jobs](#background-jobs):
    ```
    bin/sidekiq
    ```
* To set up credentials locally, run either command below using credentials found in Bitwarden.
    ```
    bundle config set --global gems.contribsys.com {BUNDLE_GEMS__CONTRIBSYS__COM}
    bundle config set --local gems.contribsys.com {BUNDLE_GEMS__CONTRIBSYS__COM}
    ```
* To run a development console for this application:
    ```
    bin/rails c
    ```

### Environment Variables

Communications Service **does not** maintain its own environment variables in our various [deployed environments](#deployment).  Instead, Above's DevOps team maintains variables in a separate infrastructure and links them in the build and deployment of the application.  Because of this, some coordination is required with DevOps when environment variables are modified.  The environment variable schema is maintained in this codebases' `manifest.yaml`, and the communication of the *values* of those variables is done via Bitwarden.

When adding a new variable, modifying the value of an existing variable, or removing a variable, we must:

1. Add the key and a sample value to `.env.sample` and `.env.test`
2. When adding or removing a variable:  update [manifest.yaml](manifest.yaml) to include/remove the variable
3. Update **each** of the Bitwarden notes to include/remove/update the value of the variable in question:
    - [Development](https://vault.bitwarden.com/#/vault?search=communications&itemId=dc7a9744-df09-4ab7-885d-b01c01360830&cipherId=3cfe8654-5c1a-4174-8688-b01f01505661)
    - [Sandbox](https://vault.bitwarden.com/#/vault?search=communications&itemId=c63b5262-f2df-4980-8859-b01f01503ab7&cipherId=fca8c4de-3261-496f-b63e-b01f0150acf4)
    - [Staging](https://vault.bitwarden.com/#/vault?search=communications&itemId=3cfe8654-5c1a-4174-8688-b01f01505661&cipherId=dc7a9744-df09-4ab7-885d-b01c01360830)
    - [Production](https://vault.bitwarden.com/#/vault?search=communications&itemId=fca8c4de-3261-496f-b63e-b01f0150acf4&cipherId=c63b5262-f2df-4980-8859-b01f01503ab7)

### Database Seeds

Database seeds are used to insert initial data into the database.  The seeding process is executed automatically as part of the `bin/setup` script as well as the `docker/entrypoint.sh`, but can also be re-executed at any time with:

* `bin/rails db:seed`: may duplicate any seed data that is not idempotently created
* `bin/rails db:seed:replant`: will wipe your local development database and re-execute seeds

The Communications Service does not currently expose a user interface for adding, editing, or removing templates or template versions -- and so uses the `db/seeds.rb` script to ensure that the necessary references to third party templates are idempotently inserted into our deployed environments.

## Local Validation

When making changes to the application, use the following commands to validate that they are working as expected and match the coding conventions for this repo:

* Run the automated test suite:
    ```
    bin/rspec
    ```
  * Run a single test:
      ```
      bin/rspec [PATH_TO_TEST_FILE_OR_DIRECTORY]
      ```
  * Run a single scenario within test:
      ```
      bin/rspec [PATH_TO_TEST_FILE_OR_DIRECTORY]:[LINE_NUMBER_OF_TARGET_SCENARIO]
      ```
* Run the Rubocop static analysis checks:
    ```
    bin/rubocop
    ```
  * Automatically correct all "correctable" issues:
      ```
      bin/rubocop -aD
      ```
* Run the Brakeman static analysis checks:
    ```
    bin/brakeman
    ```

## Deployment

This application is hosted at the following domain for each environment:

* Sandbox: https://communications-sandbox.abovelending.com
* Staging: https://communications-stage.abovelending.com
* Production https://communications.abovelending.com

### Sandbox and Staging

Deployment for Sandbox and Staging environments is done manually via Github Actions. In order to trigger a deployment, navigate to the repository in Github, to the "Actions" pane using Github's top-navigation, and then select "Manual Deployment Workflow." Or try [following this link](https://github.com/Above-Lending/communications-service/actions/workflows/manual-deployment.yaml).

From there:

1. Near the top-right, open the "Run Workflow" menu.
1. This opens a drop-down allowing for the selection of a branch or tag, as well as the "Destination Environment."
1. Select the branch or tag, e.g., `main`.
1. Then select the "Destination Environment," e.g., `sandbox` or `stage`.  **Do not** select `prod` as part of this process; if you need to deploy to production, instead follow the steps [outlined below](#production).
1. Click "Run Workflow." This triggers the deployment workflow, which may be monitored by refreshing the page and then selecting the now-running "Manual Deployment Workflow" from the middle table.

### Production

Deployment of the Production environment is done manually via Github Actions, and is executed by the product owner responsible for the feature(s) being deployed. It uses the same "[Manual Deployment Workflow](https://github.com/Above-Lending/communications-service/actions/workflows/manual-deployment.yaml)" as Sandbox and Staging deployments, but with a few extra steps, as follows:

1. As a developer, from your local environment run, the following command to generate and push a tag to Github
```
 bin/rails tag
```
1. Follow the link from the command or visit the "[Tags](https://github.com/Above-Lending/communications-service/tags)" section of Github for the Communications Service repository.
1. Click "Create release from tag" near the top right.
1. Near the release description, click "Generate release notes." This will automatically fill-in the description of the released with a list of commits indicating the changes contained in the release.
1. Next, **mark** "Set as a pre-release" and ensure that "Set as the latest release" is **unmarked**, then hit "Publish release." Announce the release in our team's slack channel with a link back to the github release that was just created.
1. Announce the intention to deploy in our team's slack channel first, with a link to the release, and request that your product owner execute the release.
1. As in Staging and Sandbox, the product owner will make use of the "[Manual Deployment Workflow](https://github.com/Above-Lending/communications-service/actions/workflows/manual-deployment.yaml)":
    1. Near the top-right, open the "Run Workflow" menu.
    1. This opens a drop-down allowing for the selection of a branch or tag, as well as the "Destination Environment."
    1. Select the tag that represents the release, e.g., `2145.***********`
    1. Then select the `prod` "Destination Environment."
    1. Click "Run Workflow." This triggers the deployment workflow, which may be monitored by refreshing the page and then selecting the now-running "Manual Deployment Workflow" from the middle table.
1. After deployment is completed, return to the release in github. Update it, **unmarking** "Set as a pre-release", and **marking** "Set as the latest release."
1. Finally, back in Jira, move the cards in the "Accepted" Column that correspond to the release over to "Released."

### .ci/

The `.ci/` folder is maintained by the devops team and can be safely ignored by folks working on the application itself.

## Background Jobs

We are using [Sidekiq](https://github.com/sidekiq/sidekiq) to execute and manage background jobs within this application. A web dashboard is hosted under the `/sidekiq` route and is secured via access to the corresponding environment's VPN. In development environments jobs must be run by executing the sidekiq process (`bin/sidekiq`). In deployed environments (e.g. staging, production, etc) a separate pod will be used to isolate the execution of background jobs from the web server pod.

## API Documentation

API docs are generated to the [swagger](http://swagger.io/) specification with the [rswag gem](https://github.com/rswag/rswag). Rswag DSL is used in request specs to define the output of documentation, which can be reached at `localhost:3000/api-docs`

example request spec file:

> `spec/requests/ping_request_spec.rb`

Run this command to generate the yml file that is used to display the documentation pages.

`$ rails rswag`

### Authentication with rswag testing DSL

RSwag is configured in `swagger_helper.rb` to know about the application's authentication methods, but parameters will need to be set in a request spec in order to authenticate properly.

```ruby
# required line in https method block of swagger test setup

security [{ token: [] }]

# request spec file example, ping_request_spec.rb

RSpec.describe 'ping_request', type: :request do
  path '/ping_request' do
    get('index ping') do
      tags 'Ping'
      consumes 'application/json'
      security [{ token: [] }]
      # ...

# Auth header must be set in the response block

let(:Authorization) { "Token #{ENV.fetch('SOME_TOKEN')}" }

# example, ping_request_spec.rb
  response '202', 'successful' do
    let(:Authorization) { "Token #{ENV.fetch('SOME_TOKEN')}" }
    # ...
```

### Postgres Debugging Tools
Two debugging UIs are available at the following endpoints:

* [PGHero](https://github.com/ankane/pghero) is available at `/pghero`
* [Rails PG Extras](https://github.com/pawurb/rails-pg-extras) is available at `/pg_extras`

## Timestamps
Our friends on the data team want all timestamps to be stored in Postgres with a timezone. To satisfy that request, we should use `timestamptz` (instead of `datetime`) when adding a new
datetime attribute to one of our rails models. Doing this makes sure the structure of the date in Postgres is stored using `TIMESTAMP WITH TIME ZONE`.
