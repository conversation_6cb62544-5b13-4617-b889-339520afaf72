# These environment variables are needed to run the app locally.
# Copy these into a file named .env.development to have them loaded automatically.
AURORA_HOST=localhost
AURORA_PORT=5432
DATABASE_USERNAME=communications_service
DATABASE_PASSWORD=password

AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=

SOLUTIONS_BY_TEXT_CLIENT_ID=
SOLUTIONS_BY_TEXT_CLIENT_SECRET=
SOLUTIONS_BY_TEXT_BASE_URL=https://t2c-api-stage.solutionsbytext.com
SOLUTIONS_BY_TEXT_AUTH_URL=https://login-stage.solutionsbytext.com
SOLUTIONS_BY_TEXT_GROUP_ID=b5191305-4625-4c43-94f4-369244b87545

FLIPPER_UI_SECRET=

IDENTITY_SERVICE_AUTH_HEADER=X-Identity-Service-Authorized
