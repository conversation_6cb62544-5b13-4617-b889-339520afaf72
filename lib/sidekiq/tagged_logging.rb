# frozen_string_literal: true

module Sidekiq
  module TaggedLogging
    TAGGED_LOGGING = 'tag_logging'
    class Client
      include Sidekiq::ClientMiddleware
      # job the full job payload
      # * @see https://github.com/sidekiq/sidekiq/wiki/Job-Format
      #
      # yield the next middleware in the chain or the enqueuing of the job
      def call(_, job, _, _)
        named_tags = ::Rails.logger.named_tags
        job[TAGGED_LOGGING] = named_tags
        yield
      end
    end

    class Server
      include Sidekiq::ServerMiddleware

      def call(_, job, _, &)
        named_tags = job[TAGGED_LOGGING] || {}
        SemanticLogger.tagged(**named_tags, &)
      end
    end
  end
end
