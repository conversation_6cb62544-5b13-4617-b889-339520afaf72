# frozen_string_literal: true

require 'logstop_guard'

class LogstopJsonFormatter < SemanticLogger::Formatters::<PERSON>son
  # Override implementation from SemanticLogger::Formatters::Raw to scrub the message
  def message
    hash[:message] = LogstopGuard.scrub!(log.cleansed_message) if log.message
  end

  # Override implementation from SemanticLogger::Formatters::Raw to sanitize the payload
  def payload
    return unless log.payload.respond_to?(:empty?) && !log.payload.empty?

    # WARNING: The sanitization process has the potential to mutate the provided object. Cloning
    # this object is crucial to avoid unexpected side effects from including data in our logs.
    hash[:payload] = LogstopGuard.sanitize_object!(log.payload.deep_dup)
  end
end
