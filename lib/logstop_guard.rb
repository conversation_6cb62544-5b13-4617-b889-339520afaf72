# frozen_string_literal: true

class LogstopGuard
  FILTERED_SUBSTITUTION = '[FILTERED]'
  SENSITIVE_KEY_SUFFIXES = %w[_number _numbers ssn authorization recipient email].freeze

  #  These regexes use the positive lookbehind feature to identify
  #  the group of characters that we want to filter.  They follow the form:
  #  Matches groups that begin with START_PATTERN
  #    (?<=START_PATTERN)
  #  Collecting zero or more characters until...
  #    .*?
  #  The matching END_PATTERN
  #  (?=END_PATTERN)
  VALUE_PATTERNS = SENSITIVE_KEY_SUFFIXES.flat_map do |key_suffix| # rubocop:disable Metrics/BlockLength
    [
      # Unquoted JSON suffix - Example: {test_key_suffix:"sensitive_value"}
      %r|
        (?<=#{key_suffix}:)
          .*?
        (?=[,}])
      |ix,
      # Escaped, double quote JSON suffix - Example: {\"test_key_suffix\":\"sensitive_value\"}
      %r|
        (?<=#{key_suffix}\\":)
          .*?
        (?=[,}])
      |ix,
      # Literal, double quote JSON suffix - Example: {"test_key_suffix":"sensitive_value"}
      %r|
        (?<=#{key_suffix}":)
          .*?
        (?=[,}])
      |ix,
      # Escaped, single quote JSON suffix - Example: {\'test_key_suffix\':\'sensitive_value\'}
      # To validate this case in an irb session, you'll need to double escape the backslashes to ensure they're
      # maintained in the resulting string (e.g. `puts "{\\'test_key_suffix\\':\\'sensitive_value\\'}"`).
      %r|
        (?<=#{key_suffix}\\':)
          .*?
        (?=[,}])
      |ix,
      # Literal, single quote JSON suffix - Example: {'test_key_suffix':'sensitive_value'}
      %r|
        (?<=#{key_suffix}':)
          .*?
        (?=[,}])
      |ix,
      # Unquoted Ruby suffix - Example: {test_key_suffix=>"sensitive_value"}
      %r|
        (?<=#{key_suffix}=>)
          .*?
        (?=[,}])
      |ix,
      # Escaped, double quote Ruby suffix - Example: {\"test_key_suffix\"=>\"sensitive_value\"}
      %r|
        (?<=#{key_suffix}\\"=>)
          .*?
        (?=[,}])
      |ix,
      # Literal, double quote Ruby suffix - Example: {"test_key_suffix"=>"sensitive_value"}
      %r|
        (?<=#{key_suffix}"=>)
          .*?
        (?=[,}])
      |ix
    ]
  end

  def self.guard!(logger)
    Logstop.guard(logger, scrubber:)

    logger
  end

  def self.scrub!(msg)
    Logstop.scrub(msg, scrubber:)
  end

  def self.sanitize_object!(obj)
    case obj
    when Hash
      obj.each do |k, v|
        obj[k] = sensitive_key?(k) ? FILTERED_SUBSTITUTION : sanitize_object!(v)
      end
    when Array
      obj.map! { |item| sanitize_object!(item) }
    when String
      scrub!(obj)
    else
      obj
    end
  end

  def self.sensitive_key?(key)
    key.to_s.ends_with?(*SENSITIVE_KEY_SUFFIXES)
  end

  def self.scrubber
    lambda do |raw_msg|
      next raw_msg if raw_msg.blank?

      VALUE_PATTERNS.reduce(raw_msg) do |msg, pii_regex|
        # NOTE:  There appear to be certain combinations of unicode characters in logs
        #        and substrings of our sensitive values that cause Ruby's regex to throw
        #        an "invalid pattern in look-behind" exception.  This error is supposed
        #        to be reserved for variable-length look-behinds, which Ruby does not like,
        #        but appears to pop up with certain fixed-length values as well.
        #        Try it, this will break:
        #        "↳ app/jobs/test_gds_connection.rb:5:in `perform'".match(/(?<=certificate:).*?(?=[,}])/ix)
        #        Reducing further, this will break:
        #        "↳".match(/(?<=ifi:).*?(?=[,}])/ix)
        #        Change one letter in the sensitive value, though, and this will not break:
        #        "↳".match(/(?<=ifj:).*?(?=[,}])/ix)
        #        Instead of peering too deeply into the dark dark pool of regex, we'll force
        #        encode logs into ASCII instead, assuming that we don't lose much of value.
        msg.encode('us-ascii', replace: '_').gsub(pii_regex, FILTERED_SUBSTITUTION)
      end
    end
  end
end
