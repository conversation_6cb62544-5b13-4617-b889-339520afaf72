# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength
class TemplateSeedData
  class << self
    def default_text_message_templates # rubocop:disable Metrics/MethodLength
      [
        {
          key: 'first_time_dq_3_dpd',
          name: 'First Time Delinquent - 3 Days Past Due',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[],
          content: <<-MSG.squish
            First time missing a payment? We can help bring your account current and
            stop collection calls. Call {{CUSTOMER_SERVICE_PHONE_NUMBER}} to learn more.
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'first_time_dq_6_days_until_payment',
          name: 'First Time Delinquent - 6 Days Until Payment',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[],
          content: <<-MSG.squish
            Above Lending: We can help bring your past-due account current. Call us
            today to learn about this one-time offer! {{CUSTOMER_SERVICE_PHONE_NUMBER}}.
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'first_time_dq_generic',
          name: 'First Time Delinquent - Generic',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[],
          content: <<-MSG.squish
            Above Lending: Call us today to take advantage of our one-time offer to
            bring your account current! {{CUSTOMER_SERVICE_PHONE_NUMBER}}.
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_1_day',
          name: 'Payment Past Due - 1 Day',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name payment_amount],
          content: <<-MSG.squish
            Hi ${first_name}, your payment to Above Lending scheduled for ${payment_date} was unsuccessful, and you are now past due.
            To avoid falling behind, please visit abovelending.com/signin or call us at {{CUSTOMER_SERVICE_PHONE_NUMBER}} to reschedule.
            http://go.abovelending.com/important-disclosures
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_5_days',
          name: 'Payment Past Due - 5 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name payment_amount],
          content: <<-MSG.squish
            {{first_name}}, your Above Lending loan payment of ${{payment_amount}} is past due.
            Please call us at {{CUSTOMER_SERVICE_PHONE_NUMBER}} or visit abovelending.com/signin to make your payment.
            http://go.abovelending.com/important-disclosures
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_10_days',
          name: 'Payment Past Due - 10 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Above Lending: We still haven't received your loan payment.
            To make a payment please call {{CUSTOMER_SERVICE_PHONE_NUMBER}}.
            http://go.abovelending.com/important-disclosures
            Thanks!
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_20_days',
          name: 'Payment Past Due - 20 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Above Lending: Questions regarding your loan payment?
            Feel free to give us a call at {{CUSTOMER_SERVICE_PHONE_NUMBER}}.
            http://go.abovelending.com/important-disclosures
            Thanks!
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_30_days',
          name: 'Payment Past Due - 30 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Above Lending: Your loan payment remains past due and may be reported as late.
            Please call {{CUSTOMER_SERVICE_PHONE_NUMBER}} to make your payment.
            http://go.abovelending.com/important-disclosures
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_45_days',
          name: 'Payment Past Due - 45 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Above Lending: We are here to help!
            Please call us at {{CUSTOMER_SERVICE_PHONE_NUMBER}} to make a payment or discuss your repayment options.
            http://go.abovelending.com/important-disclosures
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_60_days',
          name: 'Payment Past Due - 60 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Above Lending: We still haven't received your payment.
            To discuss repayment options, please call us at {{CUSTOMER_SERVICE_PHONE_NUMBER}}.
            http://go.abovelending.com/important-disclosures
            Thanks!
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_75_days',
          name: 'Payment Past Due - 75 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Above Lending: Your loan payment remains past due.
            Please call us immediately at {{CUSTOMER_SERVICE_PHONE_NUMBER}} to schedule your payment.
            http://go.abovelending.com/important-disclosures
            Thanks!
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'payment_past_due_90_days',
          name: 'Payment Past Due - 90 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Above Lending: Questions regarding your loan payment?
            Call us at {{CUSTOMER_SERVICE_PHONE_NUMBER}} and a friendly representative will be happy to help.
            http://go.abovelending.com/important-disclosures
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'setup_above_contact_request',
          name: 'Setup Above Contact Request',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: [],
          content: <<-MSG.squish
            Hi from Above Lending, and congrats on your loan offer!
            We'll be reaching out shortly, so please add ************ to your contacts so you don't miss our call.
            Excited to speak with you soon!
            {{TEXT_MESSAGE_OPT_OUT}}
          MSG
        },
        {
          key: 'contract_approval_message',
          name: 'Contract Approval Message',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: ['link'],
          content: <<-MSG.squish
            Hey there! Above Lending here with fantastic news - you've been approved!
            Take a quick look and sign your loan agreement here > {{link}}. If you'd
            like to opt out, just reply STOP.
          MSG
        }
      ]
    end

    def default_email_templates # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      [
        {
          key: 'annual_privacy_notice',
          name: 'Annual Privacy Notice',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: [],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:annual_privacy_notice],
          override_suppression: true
        },
        {
          key: 'bbb_consumer_review',
          name: 'BBB Consumer Review Invitation',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:bbb_consumer_review]
        },
        { # Deprecated.  Cloned into welcome_agl.
          key: 'welcome',
          name: 'Welcome Email',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[auto_payment_type first_name first_payment_on last_name next_scheduled_payment_amount
                        payment_frequency],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:welcome]
        },
        {
          key: 'welcome_agl',
          name: 'Welcome Email (AGL)',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[auto_payment_type first_name first_payment_on last_name next_scheduled_payment_amount
                        payment_frequency loan_maturity_date],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:welcome_agl]
        },
        {
          key: 'welcome_upl',
          name: 'Welcome Email (UPL)',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[auto_payment_type first_name first_payment_on last_name next_scheduled_payment_amount
                        payment_frequency],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:welcome_upl]
        },
        {
          key: 'payoff',
          name: 'Payoff Email',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:payoff],
          override_suppression: true
        },
        {
          key: 'charge_off_notice',
          name: 'Charge Off Notice Email',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name investor_name loan_display_id originator_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:charge_off_notice],
          override_suppression: true
        },
        {
          key: 'debt_validation',
          name: 'Debt Validation',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[
            full_name address_line1 address_line2 address_city address_state
            address_zip_code loan_display_id message_send_date
            investor_name itemization_date_from_contract_date
            itemization_date_from_payment_date total_payoff_amount_less_interest
            total_interest_due total_credited total_payoff_amount total_amount_due
            originator_name message_send_date_plus30
          ],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:debt_validation],
          override_suppression: true
        },
        {
          key: 'due_date_change',
          name: 'Due Date Change',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name next_payment_date full_due_amount loan_display_id],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:due_date_change],
          override_suppression: true
        },
        {
          key: 'payment_reminder_ach',
          name: 'Payment Reminder - ACH 5 Day',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name next_payment_date next_scheduled_payment_amount loan_display_id originator_name
                        investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:payment_reminder_ach],
          override_suppression: true
        },
        {
          key: 'payment_reminder_manual',
          name: 'Payment Reminder - Manual 10 Day',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name next_payment_date next_scheduled_payment_amount loan_display_id originator_name
                        investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:payment_reminder_manual],
          override_suppression: true
        },
        {
          key: 'payment_posted',
          name: 'Payment Posted',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name last_payment_amount loan_display_id originator_name investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:payment_posted],
          override_suppression: true
        },
        {
          key: 'payment_returned',
          name: 'Payment Returned',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[full_name payment_amount failure_date bank_name last_four return_code investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:payment_returned],
          override_suppression: true
        },
        {
          key: 'payment_scheduled',
          name: 'Payment Scheduled',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[bank_name first_name last_four message_send_date payment_amount payment_date],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:payment_scheduled],
          override_suppression: true
        },
        {
          key: 'missed_payment',
          name: 'Missed Payment',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name last_payment_amount last_payment_due_date loan_display_id originator_name
                        investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:missed_payment]
        },
        {
          key: 'past_due_1_day_new',
          name: 'Past Due - 1 Day  (New for May 2024)',
          topic: Template::DetailInfo::DELINQUENCY_TOPIC,
          variables: %w[first_name total_amount_due payment_date originator_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_1_day_new],
          override_suppression: true
        },
        {
          key: 'past_due_7_days_new',
          name: 'Past Due - 7 Days  (New for May 2024)',
          topic: Template::DetailInfo::DELINQUENCY_TOPIC,
          variables: %w[first_name total_amount_due payment_date originator_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_7_days_new],
          override_suppression: true
        },
        {
          key: 'past_due_14_to_28_days_new',
          name: 'Past Due - 14 to 28 Days (New for May 2024)',
          topic: Template::DetailInfo::DELINQUENCY_TOPIC,
          variables: %w[first_name total_amount_due originator_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_14_to_28_days_new],
          override_suppression: true
        },
        {
          key: 'past_due_35_to_56_days_new',
          name: 'Past Due - 35 to 56 Days (New for May 2024)',
          topic: Template::DetailInfo::DELINQUENCY_TOPIC,
          variables: %w[first_name total_amount_due originator_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_35_to_56_days_new],
          override_suppression: true
        },
        {
          key: 'past_due_63_to_84_days_new',
          name: 'Past Due - 63 to 84 Days (New for May 2024)',
          topic: Template::DetailInfo::DELINQUENCY_TOPIC,
          variables: %w[first_name total_amount_due originator_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_63_to_84_days_new],
          override_suppression: true
        },
        {
          key: 'past_due_91_to_115_days_new',
          name: 'Past Due - 91 to 115 Days (New for May 2024)',
          topic: Template::DetailInfo::DELINQUENCY_TOPIC,
          variables: %w[first_name total_amount_due days_past_due originator_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_91_to_115_days_new],
          override_suppression: true
        },
        {
          key: 'past_due_7_to_28_days',
          name: 'Past Due - 7 to 28 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name total_amount_due loan_display_id originator_name investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_7_to_28_days]
        },
        {
          key: 'past_due_35_to_56_days',
          name: 'Past Due - 35 to 56 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name total_amount_due loan_display_id originator_name investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_35_to_56_days]
        },
        {
          key: 'past_due_63_to_84_days',
          name: 'Past Due - 63 to 84 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name total_amount_due loan_display_id originator_name investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_63_to_84_days]
        },
        {
          key: 'past_due_90_to_115_days',
          name: 'Past Due - 90 to 115 Days',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name total_amount_due days_past_due loan_display_id originator_name investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:past_due_90_to_115_days]
        },
        {
          key: 'notice_of_adverse_action',
          name: 'Notice of Adverse Action',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[first_name applicant_name applicant_address applicant_city_state_zip
                        display_oh_discrimination_disclosure date credit_report_date gds_decline_reason
                        gds_decline_reasons gds_score factors is_fcra is_crb is_equifax],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:notice_of_adverse_action],
          override_suppression: true
        },
        {
          key: 'notice_of_default_ks',
          name: 'Notice of Default - KS',
          override_suppression: true,
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[
            address_city
            address_line1
            address_state
            address_zip_code
            contract_date
            first_name
            full_name
            investor_name
            last_payment_amount
            last_payment_due_date
            loan_display_id
            message_send_date
            message_send_date_plus28
            originator_name
            total_amount_due
          ],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:notice_of_default_ks]
        },
        {
          key: 'notice_of_default_wi',
          name: 'Notice of Default - WI',
          override_suppression: true,
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[
            address_city
            address_line1
            address_state
            address_zip_code
            contract_date
            first_name
            full_name
            investor_name
            last_payment_amount
            last_payment_due_date
            message_send_date
            message_send_date_plus28
            originator_name
            past_payment_amounts_list
            past_payment_dates_list
            past_payment_list
            total_amount_due
          ],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:notice_of_default_wi]
        },
        {
          key: 'notice_of_default_mo',
          name: 'Notice of Default - MO',
          override_suppression: true,
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[
            address_city
            address_line1
            address_state
            address_zip_code
            contract_date
            first_name
            full_name
            investor_name
            last_payment_amount
            last_payment_due_date
            loan_display_id
            message_send_date
            message_send_date_plus28
            originator_name
            total_amount_due
          ],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:notice_of_default_mo]
        },
        {
          key: 'notice_of_incomplete_application',
          name: 'Notice of Incomplete Application',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[full_name date expiration_date],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:notice_of_incomplete_application],
          override_suppression: true
        },
        {
          key: 'statement_of_rights_dc',
          name: 'Statement of Rights - DC',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[message_send_date loan_display_id first_name originator_name investor_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:statement_of_rights_dc],
          override_suppression: true
        },
        {
          key: 'post_funding_survey',
          name: 'Post Funding Survey',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name loan_display_id],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:post_funding_survey]
        },
        {
          key: 'first_time_dq_1',
          name: 'First Time Delinquency - 1',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:first_time_dq_1] # rubocop:disable Naming/VariableNumber
        },
        {
          key: 'first_time_dq_2',
          name: 'First Time Delinquency - 2',
          topic: Template::DetailInfo::CUSTOMER_CARE_TOPIC,
          variables: %w[first_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:first_time_dq_2] # rubocop:disable Naming/VariableNumber
        },
        {
          key: 'pre_offer_dropoff',
          name: 'Pre-Offer Dropoff',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[code link service_entity_name subject],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:pre_offer_dropoff]
        },
        {
          key: 'post_offer_dropoff',
          name: 'Post-Offer Dropoff',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[first_name offer_amount link service_entity_name subject],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:post_offer_dropoff]
        },
        {
          key: 'upl_loan_approved',
          name: 'UPL loan approved email',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[first_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:upl_loan_approved]
        },
        {
          key: 'upl_offer',
          name: 'UPL offer email',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[first_name link display_oh_discrimination_disclosure offers],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:upl_offer]
        },
        {
          key: 'upl_onboarding',
          name: 'UPL account setup and reset password email',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[full_name link],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:upl_onboarding]
        },
        {
          key: 'ach_authorization_single_payment',
          name: 'ACH Authorization - Single Payment',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name message_send_date last_name bank_name last_four payment_amount payment_date],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:ach_authorization_single_payment],
          override_suppression: true
        },
        {
          key: 'ach_authorization_recurring_payment',
          name: 'ACH Authorization - Recurring Payment',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name message_send_date last_name bank_name last_four payment_amount payment_date
                        payment_frequency],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:ach_authorization_recurring_payment],
          override_suppression: true
        },
        {
          key: 'offer_expiration_retargeting_social_proof',
          name: 'Retargeting - Offer Expiration - Social Proof',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[code date first_name link service_entity_name service_entity_shortcode token],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:offer_expiration_retargeting_social_proof],
          limit_per_borrower: 2
        },
        {
          key: 'offer_expiration_retargeting_fomo',
          name: 'Retargeting - Offer Expiration - Fear of Missing Out',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[code date first_name link service_entity_name service_entity_shortcode token],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:offer_expiration_retargeting_fomo],
          limit_per_borrower: 2
        },
        {
          key: 'offer_expiration_retargeting_no_prepayment',
          name: 'Retargeting - Offer Expiration - No Prepayment',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[code date first_name link service_entity_name service_entity_shortcode token],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:offer_expiration_retargeting_no_prepayment],
          limit_per_borrower: 2
        },
        {
          key: 'identity_welcome',
          name: 'Welcome Email (Identity Service)',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[full_name link],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:identity_welcome],
          override_suppression: true,
          attach_vcf_card: true
        },
        {
          key: 'identity_welcome_and_set_password_ipl',
          name: 'Welcome and Set Password Email for IPL Loans (Identity Service)',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[full_name link],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:identity_welcome_and_set_password_ipl],
          override_suppression: true,
          attach_vcf_card: true
        },
        {
          key: 'identity_welcome_and_set_password_non_ipl',
          name: 'Welcome and Set Password Email for Non-IPL Loans (Identity Service)',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[full_name link],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:identity_welcome_and_set_password_non_ipl],
          override_suppression: true,
          attach_vcf_card: true
        },
        {
          key: 'identity_welcome_and_set_password_web',
          name: 'Welcome and Set Password Email for Web Loans (Identity Service)',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[full_name link],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:identity_welcome_and_set_password_web],
          override_suppression: true,
          attach_vcf_card: true
        },
        {
          key: 'identity_reset_password_instructions',
          name: 'Reset Password Instructions (Identity Service)',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[full_name link],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:identity_reset_password_instructions],
          override_suppression: true,
          attach_vcf_card: true
        },
        {
          key: 'information_and_disclosure',
          name: 'Information and Disclosure',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[first_name],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:information_and_disclosure],
          override_suppression: true
        },
        {
          key: 'ipl_offers',
          name: 'IPL Offers',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[first_name offers],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:ipl_offers],
          override_suppression: true
        },
        {
          key: 'loan_approved',
          name: 'Loan Approved',
          topic: Template::DetailInfo::ORIGINATIONS_TOPIC,
          variables: %w[first_name link],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:loan_approved],
          override_suppression: true
        },
        {
          key: 'stamped_loan_agreement',
          name: 'Stamped Loan Agreement',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[first_name lp_loan_status loan_agreement_files],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:stamped_loan_agreement],
          override_suppression: true
        },
        {
          key: 'good_bye_letter',
          name: 'Good Bye Letter',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[email unified_id charge_off_date charge_off_balance balance first_name last_name
                        address_line1 address_line2 address_city address_state address_zip_code sale_date
                        last_4_unified_id],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:good_bye_letter],
          override_suppression: true
        },
        {
          key: 'good_bye_letter_velocity',
          name: 'Good Bye Letter',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[email unified_id charge_off_date charge_off_balance balance first_name last_name
                        address_line1 address_line2 address_city address_state address_zip_code sale_date
                        last_4_unified_id],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:good_bye_letter_velocity],
          override_suppression: true
        },
        {
          key: 'good_bye_letter_quantum',
          name: 'Good Bye Letter',
          topic: Template::DetailInfo::SERVICING_TOPIC,
          variables: %w[email unified_id balance first_name last_name
                        address_line1 address_line2 address_city address_state address_zip_code sale_date
                        last_4_unified_id],
          vendor_id: Rails.application.config_for(:sendgrid).template_ids[:good_bye_letter_quantum],
          override_suppression: true
        }
      ]
    end

    def ensure_for(key:, name:, topic:, **options)
      limit_per_borrower, override_suppression, attach_vcf_card =
        options
        .with_defaults(limit_per_borrower: nil, override_suppression: false, attach_vcf_card: false)
        .values_at(:limit_per_borrower, :override_suppression, :attach_vcf_card)

      template = Template.find_by("detail_info->>'key' = :key", key:)
      if template.blank?
        return Template.create!(detail_info: { key:, name:, topic:, limit_per_borrower:,
                                               override_suppression:, attach_vcf_card: })
      end

      template.detail_info.name = name
      template.detail_info.topic = topic
      template.detail_info.limit_per_borrower = limit_per_borrower
      template.detail_info.override_suppression = override_suppression
      template.detail_info.attach_vcf_card = attach_vcf_card
      template.save!
      template
    end

    def seed!(verbose: false)
      seed_emails!(verbose:)
      seed_text_messages!(verbose:)
      puts('All seed operations completed.') if verbose
    end

    def seed_emails!(verbose: false) # rubocop:disable Metrics/AbcSize
      ########
      # NOTE #
      ########
      # This will always update (or create, if new) the existing active
      # template version for a given template.  This means that later changes
      # to a given template version in this seed file (e.g., to modify variables,
      # content, or vendor id) will overwrite instead of replace a template version.
      # This is bad because it destroys our ability to confidently recreate a given
      # message's body based on the templates in our database.
      # When it comes time to change an existing template version, we must update this seed script to:
      # 1. Find the existing active template version and deactivate it
      # 2. Populate the new template version and activate it

      puts("Seeding #{default_email_templates.count} default email templates...") if verbose
      default_email_templates.each do |attributes|
        template = ensure_for(**attributes.slice(:key, :name, :topic, :limit_per_borrower,
                                                 :override_suppression, :attach_vcf_card))

        active_template_version = template.template_versions.find do |template_version|
          template_version.detail_info.status == TemplateVersion::DetailInfo::ACTIVE_STATUS
        end
        active_template_version ||= TemplateVersion.new(
          template:,
          detail_info: { status: TemplateVersion::DetailInfo::ACTIVE_STATUS }
        )
        active_template_version.detail_info.variables = attributes[:variables]
        active_template_version.detail_info.vendor_id = attributes[:vendor_id]
        active_template_version.save!
      end
    end

    def seed_text_messages!(verbose: false) # rubocop:disable Metrics/AbcSize
      ########
      # NOTE #
      ########
      # This will always update (or create, if new) the existing active
      # template version for a given template.  This means that later changes
      # to a given template version in this seed file (e.g., to modify variables,
      # content, or vendor id) will overwrite instead of replace a template version.
      # This is bad because it destroys our ability to confidently recreate a given
      # message's body based on the templates in our database.
      # When it comes time to change an existing template version, we must update this seed script to:
      # 1. Find the existing active template version and deactivate it
      # 2. Populate the new template version and activate it
      puts("Seeding #{default_text_message_templates.count} default text message templates...") if verbose

      default_text_message_templates.each do |attributes|
        template = ensure_for(**attributes.slice(:key, :name, :topic, :limit_per_borrower))

        active_template_version = template.template_versions.find do |template_version|
          template_version.detail_info.status == TemplateVersion::DetailInfo::ACTIVE_STATUS
        end
        active_template_version ||= TemplateVersion.new(
          template:,
          detail_info: { status: TemplateVersion::DetailInfo::ACTIVE_STATUS }
        )
        active_template_version.detail_info.variables = attributes[:variables]
        active_template_version.detail_info.content = attributes[:content]
        active_template_version.save!
      end
    end
  end
end
# rubocop:enable Metrics/ClassLength
