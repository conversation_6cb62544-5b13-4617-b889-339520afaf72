# frozen_string_literal: true

class SidekiqErrorInterceptor
  include Sidekiq::ServerMiddleware

  class RetryError < StandardError
    attr_reader :wrapped_exception

    def initialize(message:, wrapped_exception:)
      @wrapped_exception = wrapped_exception

      super(message)
    end
  end

  # @param [Object] job_instance the instance of the job that was queued
  # @param [Hash] job_payload the full job payload
  #   * @see https://github.com/sidekiq/sidekiq/wiki/Job-Format
  # @param [String] queue the name of the queue the job was pulled from
  # @yield the next middleware in the chain or worker `perform` method
  # @return [Void]
  def call(job_instance, job_payload, queue)
    yield
  rescue StandardError => e
    # NOTE:  We'll raise the original error if we're on our last retry
    #        so that it displays correctly in the Sidekiq death queue
    #        and observability platform.
    raise e if last_retry?(job_payload)

    message = {
      job: job_instance.class.name,
      job_payload:,
      queue:,
      e:
    }

    Rails.logger.warn(
      'Sidekiq job raised exception; raising RetryError for retry.',
      **message
    )

    raise RetryError.new(message:, wrapped_exception: e)
  end

  private

  def last_retry?(job_payload)
    return false if job_payload['retry_count'].blank? || job_payload['retry'].blank?

    # NOTE:  The 'retry_count' property indicates how many times a job has _already_
    #        been retried, and is zero indexed.  An exception that causes the _first_
    #        retry will have a nil count; after the first retry the count will be 0;
    #        after the second retry it will be 1; etc.  Detecting the last retry then
    #        means to find that the retry_count is one less than the allowed retries.
    job_payload['retry_count'] == job_payload['retry'] - 1
  end
end
