# frozen_string_literal: true

module LoggingConcern
  extend ActiveSupport::Concern

  def log_info(msg, **)
    # Certain keyword args will override semantic_logger
    # and cause ArgumentError: unknown keywords:
    # for the rest of the parameters
    # https://github.com/reidmorrison/semantic_logger/blob/master/lib/semantic_logger/log.rb
    Rails.logger.info(msg, **)
  end

  def log_error(msg, **)
    Rails.logger.error(msg, **)
  end

  def log_details(method_name:, custom_msg: nil, message: nil, **)
    log_info("#{full_class_name} - #{method_name}: #{custom_msg}", **, **extract_message_traits(message))
  end

  private

  def full_class_name
    self.class.name
  end

  def extract_message_traits(message)
    return {} if message.blank?

    msg_hash = message.as_json(except: %i[created_at updated_at lock_version]).symbolize_keys
    msg_hash[:message_id] = msg_hash.delete(:id)
    msg_hash[:loan_id] = message.associated_loan_id
    msg_hash[:detail_info].delete('inputs')
    msg_hash[:detail_info].delete('recipient')

    msg_hash
  end
end
