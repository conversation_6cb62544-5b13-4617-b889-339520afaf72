# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep

/public/assets

# Ignore master key for decrypting credentials and more.
/config/master.key

# Ignore application config.
/.env
/.env.development
/.env.*local
/.alp/.env.development
/.alp/.env.sandbox
/.alp/.env.staging
/.alp/local-public.key
/.alp/sandbox-private.key
/.alp/sandbox-public.key
/.alp/staging-private.key
/.alp/staging-public.key

# Ignore ALP V2 Files
/compose.yml
/compose.development.yml
/compose.sandbox.yml
/compose.staging.yml
/local-public.key
# Hide docker keys from the volume
/public.key
/private.key


# Ignore local authentication artifacts.
/public.key
/private.key

# OS Specific files
.DS_Store

# Ignore all local test artifacts
coverage/*
junit.xml
rspec.xml
spec/examples.txt
.env.alp.override
