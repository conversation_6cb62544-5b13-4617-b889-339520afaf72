# Dash-Devs are primarily responsible for this repository
* @Above-Lending/engineering

# DevOps Team owns manifest.yaml
# https://abovelending.atlassian.net/wiki/spaces/PROD/pages/1642692650/Environment+Variables
manifest.yaml @Above-Lending/Aldo

# DevOps Team owns CD-specific YAML files
# https://www.notion.so/V2-Deployment-Methods-a016082cb26948f78481d42db9922ae1
.github/workflows/cd-build.yaml @Above-Lending/Aldo
.github/workflows/cd-deploy-prod.yaml @Above-Lending/Aldo
.github/workflows/cd-deploy-sandbox.yaml @Above-Lending/Aldo
.github/workflows/cd-deploy-stage.yaml @Above-Lending/Aldo
.github/workflows/manual-deployment.yaml @Above-Lending/Aldo
.github/workflows/main.yml @Above-Lending/Aldo

# DevOps Team needs visibility into Dockerfile changes
Dockerfile @Above-Lending/Aldo

# Data Science Team needs to approve any database changes
db/ @Above-Lending/Data-Engineering
