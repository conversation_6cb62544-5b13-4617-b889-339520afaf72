#!/usr/bin/env ruby

# This script sets up the ALP application in a Docker-based development environment.
# It mirrors the behavior of bin/setup, but is specific to the ALP setup environment.
# If changes are made here, consider whether corresponding updates are needed in bin/setup
# to maintain parity.

require 'fileutils'
require 'bundler/setup'
require 'pry'

# Path to Communications Service application root.
CS_ROOT = File.expand_path('../..', __dir__)

def system!(*args)
  system(*args) || abort("\n== Command #{args} failed ==")
end

puts "\n== Running .alp/bin/app_setup =="
FileUtils.chdir CS_ROOT do
  puts "\n== Preparing database =="

  puts '== Dropping Database =='
  # Will fail to drop if we're starting from scratch
  system 'bin/rails db:drop'

  puts '== Creating Database =='
  system! 'bin/rails db:create'

  puts '== Load DB Schema for Communications service =='
  system! 'bin/rails db:schema:load'

  # Preparing all test databases: communications database
  puts "\n== Preparing test databases =="
  system! 'bin/rails db:test:prepare'

  puts "\n== Seeding DBs =="
  system! 'bin/rails db:seed'

  puts "\n== Building initial static assets =="
  system! 'bin/rails assets:precompile'

  puts "\n== Removing old logs and tempfiles =="
  system! 'bin/rails log:clear tmp:clear'
end
