#!/usr/bin/env ruby

# This script is a way to set up or update Above Lending Platform (ALP) development environment automatically.
# This will update application_management_system.
#
# This script is idempotent, so that you can run it at any time and get an expectable outcome.
# Add necessary setup steps to this file.

# Script specific gems
require 'fileutils'
require 'json'

# Path to all application roots.
COMMS_ROOT = File.expand_path('../..', __dir__)

def system!(*args)
  system(*args) || abort("\n== Command #{args} failed ==")
end

# Start Communications Service Databases:
FileUtils.chdir COMMS_ROOT do
  puts "\n== Setting up Communications Service =="

  puts '== Copying public & private keys =='
  system! 'rm -f ./.alp/local-public.key; cp ../alp-development/public.key ./.alp/local-public.key' # symlinks do not work

  puts "\n== Creating minio buckets"
  system! '.alp/bin/create_minio_buckets'

  puts '== Building Docker Image for alp'
  system! 'docker compose -f compose.yml build --no-cache alp'

  puts '== Bundling inside Docker and volume =='
  system! 'docker compose -f compose.yml run --rm alp command bundle'

  puts "\n== Setting up rails local environment variables =="
  unless File.exist?(".env.development")
    FileUtils.cp ".env.example", ".env.development"
  end

  # Migrates the primary db
  puts '== Running .alp/bin/app_setup'
  system! 'docker compose -f compose.yml run --rm alp command .alp/bin/app_setup'
end
