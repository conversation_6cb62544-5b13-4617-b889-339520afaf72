##########################################################################################
# Above Lending Platform
#
# This file contains optional environment variables to control your environment.  Defaults
# are saved in this file and is saved in our project.
##########################################################################################

# LOCAL CACHE STORE:
# Override to isolate application cache
######
# CLUSTERED_REDIS_PREFIX=communications-service
# CLUSTERED_REDIS_URI=redis://host.docker.internal:6379/3

# LOCAL SIDEKIQ REDIS:
# Override to test sidekiq jobs locally
######
# DEDICATED_REDIS_URI=redis://host.docker.internal:6379/3

# DATADOG IN DEVELOPMENT:
# Enable to allow connection to local datadog agent in development
######
# ENABLE_DATADOG_DEVELOPMENT=true
