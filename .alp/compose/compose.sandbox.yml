services:
  alp: &alp
    environment:
      ALP_ENV: sandbox
      RAILS_ENV: development
      COMMS_DATABASE_NAME: communications_service_sandbox
      HISTFILE: /usr/local/hist/.bash_history
      IRB_HISTFILE: /usr/local/hist/.irb_history
      PRY_HISTFILE: /usr/local/hist/.pry_history
    volumes:
      - comms_local_bundle:/usr/local/bundle
      - comms_assets:/rails_terraform_docker/public/assets
      - ./:/rails_terraform_docker
      - history:/usr/local/hist
      - ./.alp/bashrc:/root/.bashrc:ro
      - ./README.md:/rails_terraform_docker/tmp/caching-dev.txt
      # Environment specific volumes go here
      - ./.alp/sandbox-public.key:/keys/abovelending_public.key
    env_file:
      - ./.alp/.env.default
      - ./.alp/.env.sandbox
      - ./.alp/.env.configuration
