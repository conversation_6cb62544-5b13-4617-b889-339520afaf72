services:
  alp: &alp
    environment:
      ALP_ENV: development
      RAILS_ENV: development
      HISTFILE: /usr/local/hist/.bash_history
      IRB_HISTFILE: /usr/local/hist/.irb_history
      PRY_HISTFILE: /usr/local/hist/.pry_history
    volumes:
      - comms_local_bundle:/usr/local/bundle
      - comms_assets:/rails_terraform_docker/public/assets
      - ./:/rails_terraform_docker
      - history:/usr/local/hist
      - ./.alp/bashrc:/root/.bashrc:ro
      - ./README.md:/rails_terraform_docker/tmp/caching-dev.txt
      # Environment specific volumes go here
      - ./.alp/local-public.key:/rails_terraform_docker/public.key
    env_file:
      - ./.alp/.env.default
      - ./.alp/.env.sandbox
      - ./.alp/.env.development
      - ./.alp/.env.configuration
