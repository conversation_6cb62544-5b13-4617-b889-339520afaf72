services:
  alp: &alp
    image: communications_service
    build:
      context: .
      dockerfile: Dockerfile
      args:
        GITHUB_TOKEN: "${GITHUB_TOKEN}"
        BUNDLE_GEMS__CONTRIBSYS__COM: "${BUNDLE_GEMS__CONTRIBSYS__COM}"
        BUILD_TYPE: development
        DD_GIT_REPOSITORY_URL: "MOCK_REPO_URL"
        DD_GIT_COMMIT_SHA: "MOCK_COMMIT_SHA"
    tty: true
    stdin_open: true
    working_dir: /rails_terraform_docker
    volumes:
      - comms_local_bundle:/usr/local/bundle
      - comms_assets:/rails_terraform_docker/public/assets
      - ./:/rails_terraform_docker
      - history:/usr/local/hist
      - ./.alp/bashrc:/root/.bashrc:ro
      - ./README.md:/rails_terraform_docker/tmp/caching-dev.txt
      # Environment specific volumes go here
      - ./.alp/local-public.key:/rails_terraform_docker/public.key
    tmpfs:
      - /tmp/pids/
    command: ["start-alp-development"]
    ports:
      - 3002:3002
    # setup https://communication-service-api-local.abovelending.local/ through traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.comms.rule=Host(`communications.local.abovelending.local`)"
      - "traefik.http.routers.comms.entrypoints=websecure"
      - "traefik.http.routers.comms.tls=true"
    environment:
      ALP_ENV: DEFAULT
      HISTFILE: /usr/local/hist/.bash_history
      IRB_HISTFILE: /usr/local/hist/.irb_history
      PRY_HISTFILE: /usr/local/hist/.pry_history
    env_file:
      - ./.alp/.env.default
      - ./.alp/.env.configuration
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - alp-dev-net

  sidekiq:
    <<: *alp
    command: ["start-sidekiq"]
    ports: []

  createbuckets:
    image: minio/mc
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set myminio http://host.docker.internal:9000 aboveUser password;
      /usr/bin/mc mb --ignore-existing myminio/above-lending-communication-service-sandbox;
      /usr/bin/mc policy set public myminio/above-lending-communication-service-sandbox;
      /usr/bin/mc mb --ignore-existing myminio/above-lending-communication-service-staging;
      /usr/bin/mc policy set public myminio/above-lending-communication-service-staging;
      exit 0;
      "
    networks:
      - alp-dev-net

volumes:
  comms_local_bundle:
  comms_assets:
  history:

networks:
  alp-dev-net:
    name: alp-dev-net
    external: true
