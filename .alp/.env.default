##########################################################################################
# Above Lending Platform
#
# This file contains default environment variables to setup docker environment.
##########################################################################################

RAILS_LOG_TO_STDOUT=true

# AURORA Database
AURORA_HOST=host.docker.internal
AURORA_PORT=5432
DATABASE_USERNAME=abovelending
DATABASE_PASSWORD=abovedev

# Cache Store and Flipper
CLUSTERED_REDIS_URI=redis://host.docker.internal:6379/3
CLUSTERED_REDIS_PREFIX=communications-service

# Sidekiq Redis
DEDICATED_REDIS_URI=redis://host.docker.internal:6379/3
