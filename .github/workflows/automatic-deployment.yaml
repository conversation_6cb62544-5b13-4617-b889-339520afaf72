name: Automatic Deployment Workflow

on:
  push:
    branches:
      - 'main'
    paths-ignore:
      - .ci/**
      - .github/**
      - CODEOWNERS
      - README*

jobs:
  cd-build:
    uses: ./.github/workflows/cd-build.yaml
    secrets: inherit
    with:
      DestEnvironment: staging

  cd-deploy-staging:
    uses: ./.github/workflows/cd-deploy-ecs-staging.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: staging

  chatbot:
    uses: ./.github/workflows/chatbot.yaml
    needs: cd-deploy-staging
    secrets: inherit
    with:
      DestEnvironment: staging
