name: Manual Deployment Workflow
run-name: Manual Deployment to ${{ inputs.DestEnvironment }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      DestEnvironment:
        type: choice
        description: Destination Environment
        required: true
        options:
          - production
          - sandbox
          - staging
        default: 'sandbox'

jobs:
  cd-build:
    uses: ./.github/workflows/cd-build.yaml
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}

  cd-deploy-sandbox-ecs:
    if: inputs.DestEnvironment == 'sandbox'
    uses: ./.github/workflows/cd-deploy-ecs-sandbox.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}

  cd-deploy-staging-ecs:
    if: inputs.DestEnvironment == 'staging'
    uses: ./.github/workflows/cd-deploy-ecs-staging.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}

  cd-deploy-production-ecs:
    if: inputs.DestEnvironment == 'production'
    uses: ./.github/workflows/cd-deploy-ecs-production.yaml
    needs: [cd-build]
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
