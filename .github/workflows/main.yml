name: Ruby

on: [push]
defaults:
  run:
    shell: bash

jobs:
  rubocop:
    runs-on: ubuntu-latest
    env:
      BUNDLE_GEMS__CONTRIBSYS__COM: ${{ secrets.BUNDLE_GEMS__CONTRIBSYS__COM }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run Rubocop
        run: bundle exec rubocop --parallel
  brakeman:
    runs-on: ubuntu-latest
    env:
      BUNDLE_GEMS__CONTRIBSYS__COM: ${{ secrets.BUNDLE_GEMS__CONTRIBSYS__COM }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run brakeman
        run: bundle exec brakeman
  test:
    runs-on: ubuntu-latest
    env:
      RAILS_ENV: test
      PGHOST: localhost
      PGPASSWORD: password
      PGUSER: communications_service
      BUNDLE_GEMS__CONTRIBSYS__COM: ${{ secrets.BUNDLE_GEMS__CONTRIBSYS__COM }}
    services:
      postgres:
        image: postgres:15.10
        env:
          POSTGRES_DB: communications_service_test
          POSTGRES_USER: communications_service
          POSTGRES_PASSWORD: password
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Database setup
        run: bundle exec rails db:create && bundle exec rails db:migrate && bundle exec rails db:test:prepare
      - name: Run RSpec
        run: |
          mkdir /tmp/test-results
          bundle exec rspec --profile 10 --format RspecJunitFormatter --out /tmp/test-results/rspec.xml --format progress
      - name: Upload Coverage Artifacts
        if: ${{ !cancelled() }}
        uses: actions/upload-artifact@v4
        with:
          name: full-coverage
          path: coverage/
          include-hidden-files: true
          overwrite: true
