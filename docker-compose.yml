services:
  redis:
    image: redis:alpine
    volumes:
      - ./tmp/redis:/data
    ports:
      - 6379:6379

  alp: &alp
    image: communications_service
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUNDLE_GEMS__CONTRIBSYS__COM: "${BUNDLE_GEMS__CONTRIBSYS__COM}"
        BUILD_TYPE: development
    tty: true
    stdin_open: true
    volumes:
      - comms_local_bundle:/usr/local/bundle
      - ./:/rails_terraform_docker
    env_file:
      - ./.env.alp.override
    # Include environment variables specific to the docker version
    # NOTE: Anything here will override env_files
    environment:
      # Defaulting to ALP Development Services
      - AURORA_HOST=host.docker.internal
      - AURORA_PORT=5432
      - DATABASE_USERNAME=abovelending
      - DATABASE_PASSWORD=abovedev
      - BUNDLE_GEMS__CONTRIBSYS__COM=${BUNDLE_GEMS__CONTRIBSYS__COM}
      - FLIPPER_UI_SECRET=
    command: [ "start-alp-development" ]
    ports:
      - 3003:3002

  app:
    <<: *alp
    command: [ "start-web" ]
    depends_on:
      - redis
    environment:
      - AURORA_HOST=postgres
      - AURORA_PORT=5432
      - DATABASE_USERNAME=communications_service
      - DATABASE_PASSWORD=password
      - PORT=3002
      - RAILS_ENV=development
      - RAILS_LOG_TO_STDOUT=true
      - RAILS_SERVE_STATIC_FILES=true
      - RAILS_SKIP_ASSET_COMPILATION=true
      - RAILS_SKIP_MIGRATIONS=false
      - SECRET_KEY_BASE=true
      - AWS_REGION=us-east-1
      - DEDICATED_REDIS_URI=redis://redis:6379/1

  sidekiq:
    <<: *alp
    command: [ "start-sidekiq" ]
    ports: []

volumes:
  comms_local_bundle:
