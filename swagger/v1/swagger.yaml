---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/api/messages":
    post:
      summary: Creates a message
      tags:
      - Messages
      description: Example request body for creating a new Message in the Communications
        Service
      parameters: []
      responses:
        '201':
          description: message created
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                template_key:
                  type: string
                  description: internal key used to identify the template associated
                    with a message
                recipient:
                  type: string
                  description: identifies the recipient of a message (e.g. phone number
                    or email)
                delivery_method:
                  type: string
                  enum:
                  - SMS
                  - EMAIL
                  description: identifies how the message should be delivered (e.g.
                    SMS or email)
                inputs:
                  type: object
                  properties: {}
                attribution:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      type:
                        type: string
                        enum:
                        - APPLICATION
                        - BORROWER
                        - DECISION
                        - LOAN
                        - LOAN_INQUIRY
                        - SOURCE
            examples:
              first_time_dq_3_dpd:
                summary: First Time Dq 3 Dpd SMS
                value:
                  template_key: first_time_dq_3_dpd
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              first_time_dq_6_days_until_payment:
                summary: First Time Dq 6 Days Until Payment SMS
                value:
                  template_key: first_time_dq_6_days_until_payment
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              first_time_dq_generic:
                summary: First Time Dq Generic SMS
                value:
                  template_key: first_time_dq_generic
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_1_day:
                summary: Payment Past Due 1 Day SMS
                value:
                  template_key: payment_past_due_1_day
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs:
                    first_name: Erica
                    payment_amount: 200.5
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_5_days:
                summary: Payment Past Due 5 Days SMS
                value:
                  template_key: payment_past_due_5_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs:
                    first_name: Erica
                    payment_amount: 200.5
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_10_days:
                summary: Payment Past Due 10 Days SMS
                value:
                  template_key: payment_past_due_10_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_20_days:
                summary: Payment Past Due 20 Days SMS
                value:
                  template_key: payment_past_due_20_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_30_days:
                summary: Payment Past Due 30 Days SMS
                value:
                  template_key: payment_past_due_30_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_45_days:
                summary: Payment Past Due 45 Days SMS
                value:
                  template_key: payment_past_due_45_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_60_days:
                summary: Payment Past Due 60 Days SMS
                value:
                  template_key: payment_past_due_60_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_75_days:
                summary: Payment Past Due 75 Days SMS
                value:
                  template_key: payment_past_due_75_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_past_due_90_days:
                summary: Payment Past Due 90 Days SMS
                value:
                  template_key: payment_past_due_90_days
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              setup_above_contact_request:
                summary: Setup Above Contact Request SMS
                value:
                  template_key: setup_above_contact_request
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              contract_approval_message:
                summary: Contract Approval Message SMS
                value:
                  template_key: contract_approval_message
                  recipient: '15556663322'
                  delivery_method: SMS
                  inputs:
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              annual_privacy_notice:
                summary: Annual Privacy Notice EMAIL
                value:
                  template_key: annual_privacy_notice
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs: {}
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              bbb_consumer_review:
                summary: Bbb Consumer Review EMAIL
                value:
                  template_key: bbb_consumer_review
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              welcome:
                summary: Welcome EMAIL
                value:
                  template_key: welcome
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    auto_payment_type: AutoPay
                    first_name: Erica
                    first_payment_on: '2024-06-01'
                    last_name: Lambert
                    next_scheduled_payment_amount: 100.23
                    payment_frequency: Monthly
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              welcome_agl:
                summary: Welcome Agl EMAIL
                value:
                  template_key: welcome_agl
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    auto_payment_type: AutoPay
                    first_name: Erica
                    first_payment_on: '2024-06-01'
                    last_name: Lambert
                    next_scheduled_payment_amount: 100.23
                    payment_frequency: Monthly
                    loan_maturity_date: '2025-08-01'
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              welcome_upl:
                summary: Welcome Upl EMAIL
                value:
                  template_key: welcome_upl
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    auto_payment_type: AutoPay
                    first_name: Erica
                    first_payment_on: '2024-06-01'
                    last_name: Lambert
                    next_scheduled_payment_amount: 100.23
                    payment_frequency: Monthly
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payoff:
                summary: Payoff EMAIL
                value:
                  template_key: payoff
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              charge_off_notice:
                summary: Charge Off Notice EMAIL
                value:
                  template_key: charge_off_notice
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    investor_name: Above Funding Trust
                    loan_display_id: '********'
                    originator_name: Above Lending
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              debt_validation:
                summary: Debt Validation EMAIL
                value:
                  template_key: debt_validation
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    address_line1: 123 Main St
                    address_line2: Apt 24B
                    address_city: Chicago
                    address_state: IL
                    address_zip_code: '60606'
                    loan_display_id: '********'
                    message_send_date: '2023-08-15'
                    investor_name: Above Funding Trust
                    itemization_date_from_contract_date: '2023-07-21'
                    itemization_date_from_payment_date: '2023-07-21'
                    total_payoff_amount_less_interest: 2817.93
                    total_interest_due: 483.62
                    total_credited: 1472.86
                    total_payoff_amount: 22637.47
                    total_amount_due: 609.93
                    originator_name: Above Lending
                    message_send_date_plus30: '2023-09-14'
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              due_date_change:
                summary: Due Date Change EMAIL
                value:
                  template_key: due_date_change
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    next_payment_date: '2023-08-21'
                    full_due_amount: 732.93
                    loan_display_id: '********'
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_reminder_ach:
                summary: Payment Reminder Ach EMAIL
                value:
                  template_key: payment_reminder_ach
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    next_payment_date: '2023-08-21'
                    next_scheduled_payment_amount: 100.23
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_reminder_manual:
                summary: Payment Reminder Manual EMAIL
                value:
                  template_key: payment_reminder_manual
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    next_payment_date: '2023-08-21'
                    next_scheduled_payment_amount: 100.23
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_posted:
                summary: Payment Posted EMAIL
                value:
                  template_key: payment_posted
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    last_payment_amount: 100.21
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_returned:
                summary: Payment Returned EMAIL
                value:
                  template_key: payment_returned
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    payment_amount: 200.5
                    failure_date: '2024-06-01'
                    bank_name: Humans Credit Union
                    last_four: 8765
                    return_code: R100
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              payment_scheduled:
                summary: Payment Scheduled EMAIL
                value:
                  template_key: payment_scheduled
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    bank_name: Humans Credit Union
                    first_name: Erica
                    last_four: 8765
                    message_send_date: '2023-08-15'
                    payment_amount: 200.5
                    payment_date: '2023-08-21'
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              missed_payment:
                summary: Missed Payment EMAIL
                value:
                  template_key: missed_payment
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    last_payment_amount: 100.21
                    last_payment_due_date: '2023-07-28'
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_1_day_new:
                summary: Past Due 1 Day New EMAIL
                value:
                  template_key: past_due_1_day_new
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    payment_date: '2023-08-21'
                    originator_name: Above Lending
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_7_days_new:
                summary: Past Due 7 Days New EMAIL
                value:
                  template_key: past_due_7_days_new
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    payment_date: '2023-08-21'
                    originator_name: Above Lending
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_14_to_28_days_new:
                summary: Past Due 14 To 28 Days New EMAIL
                value:
                  template_key: past_due_14_to_28_days_new
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    originator_name: Above Lending
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_35_to_56_days_new:
                summary: Past Due 35 To 56 Days New EMAIL
                value:
                  template_key: past_due_35_to_56_days_new
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    originator_name: Above Lending
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_63_to_84_days_new:
                summary: Past Due 63 To 84 Days New EMAIL
                value:
                  template_key: past_due_63_to_84_days_new
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    originator_name: Above Lending
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_91_to_115_days_new:
                summary: Past Due 91 To 115 Days New EMAIL
                value:
                  template_key: past_due_91_to_115_days_new
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    days_past_due: 107
                    originator_name: Above Lending
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_7_to_28_days:
                summary: Past Due 7 To 28 Days EMAIL
                value:
                  template_key: past_due_7_to_28_days
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_35_to_56_days:
                summary: Past Due 35 To 56 Days EMAIL
                value:
                  template_key: past_due_35_to_56_days
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_63_to_84_days:
                summary: Past Due 63 To 84 Days EMAIL
                value:
                  template_key: past_due_63_to_84_days
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              past_due_90_to_115_days:
                summary: Past Due 90 To 115 Days EMAIL
                value:
                  template_key: past_due_90_to_115_days
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    total_amount_due: 609.93
                    days_past_due: 107
                    loan_display_id: '********'
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              notice_of_adverse_action:
                summary: Notice Of Adverse Action EMAIL
                value:
                  template_key: notice_of_adverse_action
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    applicant_name: John Doe
                    applicant_address: 123 Main St, Apt 24B
                    applicant_city_state_zip: Chicago, IL 60606
                    display_oh_discrimination_disclosure: false
                    date: 06/14/2024
                    credit_report_date: 06/14/2024
                    gds_decline_reason: Low FICO score
                    gds_decline_reasons:
                    - reason: Low FICO score
                    - reason: Loan to income ratio
                    gds_score: 486
                    factors:
                    - factor: Serious delinquency
                    - factor: Number of accounts with delinquency
                    is_fcra: true
                    is_crb: true
                    is_equifax: true
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              notice_of_default_ks:
                summary: Notice Of Default Ks EMAIL
                value:
                  template_key: notice_of_default_ks
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    address_city: Chicago
                    address_line1: 123 Main St
                    address_state: IL
                    address_zip_code: '60606'
                    contract_date: '2023-06-02'
                    first_name: Erica
                    full_name: Erica Lambert
                    investor_name: Above Funding Trust
                    last_payment_amount: 100.21
                    last_payment_due_date: '2023-07-28'
                    loan_display_id: '********'
                    message_send_date: '2023-08-15'
                    message_send_date_plus28: '2023-09-12'
                    originator_name: Above Lending
                    total_amount_due: 609.93
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              notice_of_default_wi:
                summary: Notice Of Default Wi EMAIL
                value:
                  template_key: notice_of_default_wi
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    address_city: Chicago
                    address_line1: 123 Main St
                    address_state: IL
                    address_zip_code: '60606'
                    contract_date: '2023-06-02'
                    first_name: Erica
                    full_name: Erica Lambert
                    investor_name: Above Funding Trust
                    last_payment_amount: 100.21
                    last_payment_due_date: '2023-07-28'
                    message_send_date: '2023-08-15'
                    message_send_date_plus28: '2023-09-12'
                    originator_name: Above Lending
                    past_payment_amounts_list:
                    - 345.67
                    - 345.67
                    - 345.67
                    past_payment_dates_list:
                    - '2023-05-27'
                    - '2023-06-29'
                    - '2023-07-28'
                    past_payment_list:
                    - date: '2023-05-27'
                      amount: 100.0
                    - date: '2023-06-29'
                      amount: 100.0
                    total_amount_due: 609.93
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              notice_of_default_mo:
                summary: Notice Of Default Mo EMAIL
                value:
                  template_key: notice_of_default_mo
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    address_city: Chicago
                    address_line1: 123 Main St
                    address_state: IL
                    address_zip_code: '60606'
                    contract_date: '2023-06-02'
                    first_name: Erica
                    full_name: Erica Lambert
                    investor_name: Above Funding Trust
                    last_payment_amount: 100.21
                    last_payment_due_date: '2023-07-28'
                    loan_display_id: '********'
                    message_send_date: '2023-08-15'
                    message_send_date_plus28: '2023-09-12'
                    originator_name: Above Lending
                    total_amount_due: 609.93
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              statement_of_rights_dc:
                summary: Statement Of Rights Dc EMAIL
                value:
                  template_key: statement_of_rights_dc
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    message_send_date: '2023-08-15'
                    loan_display_id: '********'
                    first_name: Erica
                    originator_name: Above Lending
                    investor_name: Above Funding Trust
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              post_funding_survey:
                summary: Post Funding Survey EMAIL
                value:
                  template_key: post_funding_survey
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    loan_display_id: '********'
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              first_time_dq_1:
                summary: First Time Dq 1 EMAIL
                value:
                  template_key: first_time_dq_1
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              first_time_dq_2:
                summary: First Time Dq 2 EMAIL
                value:
                  template_key: first_time_dq_2
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              pre_offer_dropoff:
                summary: Pre Offer Dropoff EMAIL
                value:
                  template_key: pre_offer_dropoff
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    code: XXXXX
                    link: https://abovelending.com
                    service_entity_name: NA
                    subject: NA
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              post_offer_dropoff:
                summary: Post Offer Dropoff EMAIL
                value:
                  template_key: post_offer_dropoff
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    offer_amount: "$0.00"
                    link: https://abovelending.com
                    service_entity_name: NA
                    subject: NA
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              upl_loan_approved:
                summary: Upl Loan Approved EMAIL
                value:
                  template_key: upl_loan_approved
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              upl_offer:
                summary: Upl Offer EMAIL
                value:
                  template_key: upl_offer
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    link: https://abovelending.com
                    display_oh_discrimination_disclosure: false
                    offers:
                    - lender: Above Lending
                      amount: 10000
                      term: Monthly
                      monthly_payment: 1000
                      interest_rate: 23%
                      apr: 23
                      origination_fee: 500
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              upl_onboarding:
                summary: Upl Onboarding EMAIL
                value:
                  template_key: upl_onboarding
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              ach_authorization_single_payment:
                summary: Ach Authorization Single Payment EMAIL
                value:
                  template_key: ach_authorization_single_payment
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    message_send_date: '2023-08-15'
                    last_name: Lambert
                    bank_name: Humans Credit Union
                    last_four: 8765
                    payment_amount: 200.5
                    payment_date: '2023-08-21'
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              ach_authorization_recurring_payment:
                summary: Ach Authorization Recurring Payment EMAIL
                value:
                  template_key: ach_authorization_recurring_payment
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    message_send_date: '2023-08-15'
                    last_name: Lambert
                    bank_name: Humans Credit Union
                    last_four: 8765
                    payment_amount: 200.5
                    payment_date: '2023-08-21'
                    payment_frequency: Monthly
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              offer_expiration_retargeting_social_proof:
                summary: Offer Expiration Retargeting Social Proof EMAIL
                value:
                  template_key: offer_expiration_retargeting_social_proof
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    code: XXXXX
                    date: 06/14/2024
                    first_name: Erica
                    link: https://abovelending.com
                    service_entity_name: NA
                    service_entity_shortcode: bf
                    token: NA
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              offer_expiration_retargeting_fomo:
                summary: Offer Expiration Retargeting Fomo EMAIL
                value:
                  template_key: offer_expiration_retargeting_fomo
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    code: XXXXX
                    date: 06/14/2024
                    first_name: Erica
                    link: https://abovelending.com
                    service_entity_name: NA
                    service_entity_shortcode: bf
                    token: NA
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              offer_expiration_retargeting_no_prepayment:
                summary: Offer Expiration Retargeting No Prepayment EMAIL
                value:
                  template_key: offer_expiration_retargeting_no_prepayment
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    code: XXXXX
                    date: 06/14/2024
                    first_name: Erica
                    link: https://abovelending.com
                    service_entity_name: NA
                    service_entity_shortcode: bf
                    token: NA
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              identity_welcome:
                summary: Identity Welcome EMAIL
                value:
                  template_key: identity_welcome
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              identity_welcome_and_set_password_ipl:
                summary: Identity Welcome And Set Password Ipl EMAIL
                value:
                  template_key: identity_welcome_and_set_password_ipl
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              identity_welcome_and_set_password_non_ipl:
                summary: Identity Welcome And Set Password Non Ipl EMAIL
                value:
                  template_key: identity_welcome_and_set_password_non_ipl
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              identity_welcome_and_set_password_web:
                summary: Identity Welcome And Set Password Web EMAIL
                value:
                  template_key: identity_welcome_and_set_password_web
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              identity_reset_password_instructions:
                summary: Identity Reset Password Instructions EMAIL
                value:
                  template_key: identity_reset_password_instructions
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    full_name: Erica Lambert
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              information_and_disclosure:
                summary: Information And Disclosure EMAIL
                value:
                  template_key: information_and_disclosure
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              ipl_offers:
                summary: Ipl Offers EMAIL
                value:
                  template_key: ipl_offers
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    offers:
                    - lender: Above Lending
                      amount: 10000
                      term: Monthly
                      monthly_payment: 1000
                      interest_rate: 23%
                      apr: 23
                      origination_fee: 500
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              loan_approved:
                summary: Loan Approved EMAIL
                value:
                  template_key: loan_approved
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    first_name: Erica
                    link: https://abovelending.com
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              good_bye_letter:
                summary: Good Bye Letter
                value:
                  template_key: good_bye_letter
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    email: <EMAIL>
                    unified_id: 11111333
                    charge_off_date: 2023-05-19
                    charge_off_balance: 1199.11
                    balance: 9399.10
                    first_name: Erica
                    last_name: Lambert
                    address_line_1: 804 W ROBINHOOD DR
                    address_line_2: UNIT 6
                    city: KENT
                    state_province_region: IL
                    postal_code: 1111
                    sale_date: 2023-01-19
                    last_4_unified_id: 1333
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER
              good_bye_letter_velocity:
                summary: Good Bye Letter
                value:
                  template_key: good_bye_letter_velocity
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    email: <EMAIL>
                    unified_id: 11111333
                    charge_off_date: 2023-05-19
                    charge_off_balance: 1199.11
                    balance: 9399.10
                    first_name: Erica
                    last_name: Lambert
                    address_line_1: 804 W ROBINHOOD DR
                    address_line_2: UNIT 6
                    city: KENT
                    state_province_region: IL
                    postal_code: 1111
                    sale_date: 2023-01-19
                    last_4_unified_id: 1333
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER 
              good_bye_letter_quantum:
                summary: Good Bye Letter
                value:
                  template_key: good_bye_letter_quantum
                  recipient: <EMAIL>
                  delivery_method: EMAIL
                  inputs:
                    email: <EMAIL>
                    unified_id: 11111333
                    balance: 9399.10
                    first_name: Erica
                    last_name: Lambert
                    address_line_1: 804 W ROBINHOOD DR
                    address_line_2: UNIT 6
                    city: KENT
                    state_province_region: IL
                    postal_code: 1111
                    sale_date: 2023-01-19
                    last_4_unified_id: 1333
                  attribution:
                  - id: 43059c15-a4e3-46fd-a703-ea65ff057344
                    type: LOAN
                  - id: 86f3f5d1-ee31-4b35-895c-35ebd4fc98f8
                    type: BORROWER 
    get:
      summary: Retrieve messages
      tags:
      - Messages
      description: Returns a paginated list of messages. Each message can be filtered
        by various parameters.
      parameters:
      - name: filter[attribution_id]
        in: query
        description: Filters messages whose attributed entity matches the specified
          attribution_id attribute.
        required: false
        schema:
          type: string
      - name: filter[attribution_type]
        in: query
        description: Filters messages whose attributed entity matches the specified
          attribution_type attribute.
        required: false
        schema:
          type: string
      - name: filter[recipient]
        in: query
        description: Filters messages to those with the specified recipient attribute.
        required: false
        schema:
          type: string
      - name: filter[status]
        in: query
        description: Filters messages to those with the specified status attribute.
        required: false
        schema:
          type: string
      - name: filter[delivery_method]
        in: query
        description: Filters messages to those with the specified delivery_method
          attribute.
        required: false
        schema:
          type: string
      - name: filter[recipients]
        in: query
        description: Filters messages to those with the recipient attribute is any
          one of the specified values.
        required: false
        schema:
          type: string
      - name: filter[statuses]
        in: query
        description: Filters messages to those with the status attribute is any one
          of the specified values.
        required: false
        schema:
          type: string
      - name: filter[delivery_methods]
        in: query
        description: Filters messages to those with the delivery_method attribute
          is any one of the specified values.
        required: false
        schema:
          type: string
      - name: filter[not_recipient]
        in: query
        description: Filters messages to those with the recipient attribute is NOT
          the specified value.
        required: false
        schema:
          type: string
      - name: filter[not_status]
        in: query
        description: Filters messages to those with the status attribute is NOT the
          specified value.
        required: false
        schema:
          type: string
      - name: filter[not_delivery_method]
        in: query
        description: Filters messages to those with the delivery_method attribute
          is NOT the specified value.
        required: false
        schema:
          type: string
      - name: filter[template_key]
        in: query
        description: Filters messages to those triggered with the specified template.
        required: false
        schema:
          type: string
      - name: filter[template_keys]
        in: query
        description: Filters messages to those triggered with any one of the specified
          templates.
        required: false
        schema:
          type: string
      - name: filter[created_before]
        in: query
        description: Filters messages to those whose created at timestamp is before
          the specified date/time.
        required: false
        schema:
          type: string
      - name: filter[created_after]
        in: query
        description: Filters messages to those whose created at timestamp is after
          the specified date/time.
        required: false
        schema:
          type: string
      - name: filter[updated_before]
        in: query
        description: Filters messages to those whose updated at timestamp is before
          the specified date/time.
        required: false
        schema:
          type: string
      - name: filter[updated_after]
        in: query
        description: Filters messages to those whose updated at timestamp is after
          the specified date/time.
        required: false
        schema:
          type: string
      - name: per_page
        in: query
        description: Determines the number of messages per page.
        required: false
        schema:
          type: integer
          default: 10
      - name: page
        in: query
        description: Specifies the page number.
        required: false
        schema:
          type: integer
          default: 1
      responses:
        '200':
          description: messages found
          content:
            application/json:
              schema:
                type: object
                properties:
                  messages:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Message"
                  meta:
                    type: object
                    properties:
                      count:
                        type: number
                      total_count:
                        type: number
                      per_page:
                        type: number
                      current_page:
                        type: number
                      total_pages:
                        type: number
  "/api/messages/{id}":
    get:
      summary: Retrieves a message
      tags:
      - Messages
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: message found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Message"
components:
  schemas:
    Message:
      type: object
      properties:
        template_key:
          type: string
          description: internal key used to identify the template associated with
            a message
        recipient:
          type: string
          description: identifies the recipient of a message (e.g. phone number or
            email)
        delivery_method:
          type: string
          enum:
          - SMS
          - EMAIL
          description: identifies how the message should be delivered (e.g. SMS or
            email)
        inputs:
          type: object
          description: the inputs used to populate the message template
        attribution:
          type: array
          items:
            type: object
            description: a list of entities to attribute to the message
            properties:
              id:
                type: string
                description: the id of the corresponding entity
              type:
                type: string
                enum:
                - APPLICATION
                - BORROWER
                - DECISION
                - LOAN
                - LOAN_INQUIRY
                - SOURCE
        created_at:
          type: string
          format: date-time
          description: timestamp of the initial creation of this record
        updated_at:
          type: string
          format: date-time
          description: timestamp of the most recent change made to this record
      required:
      - template_key
      - recipient
      - delivery_method
  securitySchemes:
    token:
      type: http
      scheme: token
servers:
- url: http://localhost:3002
  variables:
    defaultHost:
      default: localhost:3002
