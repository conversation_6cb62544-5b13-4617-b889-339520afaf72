# frozen_string_literal: true

require_relative 'boot'

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module CommunicationsService
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")

    # Deployed environments use a clustered redis instance, so we must pass the
    # Redis::Cluster client.  For local environments, we must use the Redis
    # client.
    #
    # Clustered uri's use the 'rediss://` protocol
    if !Rails.env.test? && ENV.fetch('CLUSTERED_REDIS_URI', nil).present?
      redis_instance = if ENV.fetch('CLUSTERED_REDIS_URI').start_with? 'rediss'
                         Redis::Cluster.new(nodes: ENV.fetch('CLUSTERED_REDIS_URI'))
                       else
                         Redis.new(url: ENV.fetch('CLUSTERED_REDIS_URI'))
                       end

      config.cache_store = :redis_cache_store, {
        redis: redis_instance,
        expires_in: 24.hours,
        namespace: ENV.fetch('CLUSTERED_REDIS_PREFIX', nil)
      }
    else
      # Use rails' memory store for caching
      config.cache_store = :memory_store
    end

    # Semantic Logging
    log_level = ENV.fetch('LOG_LEVEL', nil)&.upcase&.to_sym
    # needs to be downcase for semantic logger but upcase to check against constants
    config.log_level = Logger::Severity.constants.include?(log_level) ? log_level.downcase : :info

    config.log_tags = {
      cf_ray: ->(request) { request.headers['CF-RAY'] },
      request_id: :request_id
    }
  end
end
