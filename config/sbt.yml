default: &default
  client_id: <%= ENV.fetch('SOLUTIONS_BY_TEXT_CLIENT_ID', nil) %>
  client_secret: <%= ENV.fetch('SOLUTIONS_BY_TEXT_CLIENT_SECRET', nil) %>
  api_base_url: <%= ENV.fetch('SOLUTIONS_BY_TEXT_BASE_URL', nil) %>
  auth_base_url: <%= ENV.fetch('SOLUTIONS_BY_TEXT_AUTH_URL', nil) %>
  group_id: <%= ENV.fetch('SOLUTIONS_BY_TEXT_GROUP_ID', nil) %>

development:
  <<: *default

test:
  <<: *default

sandbox:
  <<: *default

staging:
  <<: *default

production:
  <<: *default
