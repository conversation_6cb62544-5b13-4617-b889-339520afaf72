default: &default
  api_key: <%= ENV.fetch('SENDGRID_API_KEY', nil) %>
  asm_group: <%= ENV.fetch('SENDGRID_ASM_GROUP', 32347) %>
  # Below pre-production templates are stored under a sub-account (Above Lending Sub User) in sendgrid
  template_ids:
    annual_privacy_notice: 'd-df79510383594107af0f95d8fffea713'
    bbb_consumer_review: 'd-3e5b238e1bc249068ca53e5ec662c820'
    charge_off_notice: 'd-d50704bdc6a9419ba8b68fe13731f3c3'
    debt_validation: 'd-325f9d5edffc4e7cb71485f2155aed62'
    due_date_change: 'd-222f50bbe5b0477995b75f5ec31d54a3'
    first_time_dq_1: 'd-2ef0c3594e1347c2a3dddb09cd6e43f0'
    first_time_dq_2: 'd-62b4dff9b33d4fe997d889958836f210'
    identity_welcome: 'd-a5ee67c330c4456b952e60ec1f1c2481'
    identity_welcome_and_set_password_ipl: 'd-128c614d78014a36836185daa3aee7ea'
    identity_welcome_and_set_password_non_ipl: 'd-d7b4cb73dbe34a89b6eb8bffa995d3d6'
    identity_welcome_and_set_password_web: 'd-128c614d78014a36836185daa3aee7ea'
    identity_reset_password_instructions: 'd-1448cfc1f9c04171880f20a2201ef08d'
    information_and_disclosure: 'd-bf4d4ea50d2b4be79d1db4c4f3e0e574'
    ipl_offers: 'd-f3e51a92c40e45c1b328293e01cc14c8'
    loan_approved: 'd-5f9c22899f4a4e519a6f33918d526f57'
    missed_payment: 'd-d41a1dce957d421995b41c66fb128e65'
    notice_of_adverse_action: 'd-01d4a42712f142729bc484c702eeb770'
    notice_of_default_ks: 'd-8b8004f0e33b444b8995e8108ba9148b'
    notice_of_default_mo: 'd-ef8f32cd4925407898dc716f9f56ee9a'
    notice_of_default_wi: 'd-8e1c86d898654a19b460a5919d8437b1'
    notice_of_incomplete_application: 'd-4f29d51df1a74526b28d872f8e4c1819'
    past_due_1_day_new: 'd-7ebbff8d9d0b48a7ba86d55f386a8589'
    past_due_7_days_new: 'd-04cbc1cfbec64923b7d9ef95955f45cb'
    past_due_14_to_28_days_new: 'd-dce5ec8afb7c4ef783841a4d35139543'
    past_due_35_to_56_days_new: 'd-83b34d4a305e4afbbc968aef183e208c'
    past_due_63_to_84_days_new: 'd-287d9156cf32458d93d21a0b41c8fdb8'
    past_due_91_to_115_days_new: 'd-482a4e78906440048449660784438bf0'
    past_due_7_to_28_days: 'd-bc864f84e49f49789eb1b8a205d68a6a'
    past_due_35_to_56_days: 'd-f5097337b3c247749447fd1f22c4d83a'
    past_due_63_to_84_days: 'd-790493bfcd924e258bba82d11a3e8801'
    past_due_90_to_115_days: 'd-fae3edb1e415490d86607a58291da727'
    payment_posted: 'd-f88e3afcc2c84aa69a6b75eeadaf12d2'
    payment_reminder_ach: 'd-354616e116e84802968f5b1804344d7a'
    payment_reminder_manual: 'd-837b4cb0a8394a8da7d3da72eefac61d'
    payment_returned: 'd-ba56e7e764304449a8c5d3f4e40257a2'
    payment_scheduled: 'd-056b785fbf0f46c3ac2e0044c6145da9'
    payoff: 'd-0fe8e99898484554a4542b18d33a340c'
    post_funding_survey: 'd-f722bd3d632548f7b9c9a5ee08d07648'
    statement_of_rights_dc: 'd-b1f4aa0dc74445d680b615f217cebb9e'
    welcome: 'd-2c859486cbd84f04b249e459c51a1e74'
    welcome_agl: 'd-8699032e1d5845f184f2a1c2d4d26df5' # used by Dash for servicing onboarding
    welcome_upl: 'd-144b1d51cfe14555878eae5bd3e58048' # used by Dash for servicing onboarding
    pre_offer_dropoff: 'd-1afd6b9e45ec4111b014be0d88dcfb78'
    post_offer_dropoff: 'd-e4751dfcdb8542fdb27cd94bac89cad5'
    upl_loan_approved: 'd-adf5b1d3c3834f599d87cfaf1ed88a7e'
    upl_offer: 'd-56071d77f25d4349b1f3b717ca30dd44'
    upl_onboarding: 'd-d7b4cb73dbe34a89b6eb8bffa995d3d6' # used by AMS for UPL account setup
    ach_authorization_single_payment: 'd-c9079df80b204056be96812c95efcd02'
    ach_authorization_recurring_payment: 'd-5b94b334bec5478ab4bc4a0116288663'
    offer_expiration_retargeting_social_proof: 'd-88ed84d5456749b7b7017fda753f5bd2'
    offer_expiration_retargeting_fomo: 'd-a45f314b3fbd456d99704d4e743ac44b'
    offer_expiration_retargeting_no_prepayment: 'd-e118813e278141a4bacad38254be706a'
    stamped_loan_agreement: 'd-c2486b66c5e042319ce81b08331b6ae2'
    good_bye_letter: 'd-61f607c4ef254b60a4b68896292897b4'
    good_bye_letter_velocity: 'd-61f607c4ef254b60a4b68896292897b4'
    good_bye_letter_quantum: 'd-ae6f2cbc5eeb480ca51e982f5f167d4a'

development:
  <<: *default

test:
  <<: *default

sandbox:
  <<: *default

staging:
  <<: *default

production:
  <<: *default
  asm_group: <%= ENV.fetch('SENDGRID_ASM_GROUP', 23592) %>
  template_ids:
    annual_privacy_notice: 'd-0dffb70bede44c9e92cab8fca7f1ce7b'
    bbb_consumer_review: 'd-9356fe560e5447cfa94376fb2cdd9a90'
    charge_off_notice: 'd-0b089f5aa4b940148871010f10941e5a'
    debt_validation: 'd-896cd0c63cc54457833f554538a58728'
    due_date_change: 'd-2d20fc1b023942c087dedc89e047a660'
    first_time_dq_1: 'd-3522334f76a8418497dbccce0d62732b'
    first_time_dq_2: 'd-ca0e3410fe1f456c8105add59f4f71b1'
    identity_welcome: 'd-06a40a3a860948528e0c2d0dcfba9619'
    identity_welcome_and_set_password_ipl: 'd-4abd6fd487f4471783f6a37350acc60d'
    identity_welcome_and_set_password_non_ipl: 'd-8057ae822f504266a97b15bf53390519'
    identity_welcome_and_set_password_web: 'd-4abd6fd487f4471783f6a37350acc60d'
    identity_reset_password_instructions: 'd-07e5e43ae4ef41b5bcb02ff731951fdf'
    information_and_disclosure: 'd-77aaf81b5e2642798075dcdc9fc08f8f'
    ipl_offers: 'd-951e72c15b864dc7a5484923f3f1ae7e'
    loan_approved: 'd-74f6946d0bf64462bcc46b3e1f7a54b5'
    missed_payment: 'd-812b9ac5e4f948beaa54665d6e41d6ee'
    notice_of_adverse_action: 'd-70d09e06a21d4044b49d642209ab0089'
    notice_of_default_ks: 'd-9360c14098b841379087c68ca7e397e8'
    notice_of_default_mo: 'd-0b17f067326e4ca7b95663a043b10964'
    notice_of_default_wi: 'd-45c00c578c244affbaa7d90619a988aa'
    notice_of_incomplete_application: 'd-d64795dd12ed4934bdf32cc67045ac5a'
    past_due_1_day_new: 'd-c538a89918884697874f94c7f824bab5'
    past_due_7_days_new: 'd-2f250986733d439397c7f82d3c2648b9'
    past_due_14_to_28_days_new: 'd-48b8bdd9a8fc48eeaaff3f6e08dbe85a'
    past_due_35_to_56_days_new: 'd-33b74a685b864bdaae96a734b2974853'
    past_due_63_to_84_days_new: 'd-98c711899a414a1da7dde7ed93644c30'
    past_due_91_to_115_days_new: 'd-b9dbf36b5542468e80e48cd993ce72bd'
    past_due_7_to_28_days: 'd-5dfde7798c854c268ae40b9bffa1a9ff'
    past_due_35_to_56_days: 'd-0b870c7e1fd940b5afc6fdf78d02b23e'
    past_due_63_to_84_days: 'd-81f47471eef345a2bc5100634e500c00'
    past_due_90_to_115_days: 'd-4656f156ca784dcaae1cc9a292e3c1e6'
    payment_posted: 'd-5d043e1805fe4266bce23d5b1f6eb155'
    payment_reminder_ach: 'd-96a8416162b1444f8a9ecf2c7340741c'
    payment_reminder_manual: 'd-39425783a4354a63b33d7a2d8f0f49cd'
    payment_returned: 'd-ef485fd6d1dd4b3cb8bb87e613918283'
    payment_scheduled: 'd-e7dc15539e7c4034994925b7a1dd5b48'
    payoff: 'd-0377084cd9964764869abe40f0380c68'
    post_funding_survey: 'd-44d4cb31dbf54d10a49770f27c74d72a'
    statement_of_rights_dc: 'd-1dc38f967c794bb88d557b0573975a62'
    welcome: 'd-56364051e5694d8f8eff5fb8d6d11c56'
    welcome_agl: 'd-56364051e5694d8f8eff5fb8d6d11c56'  # used by Dash for servicing onboarding
    welcome_upl: 'd-5cb52fe500af4fb987167678b1ad73c5'  # used by Dash for servicing onboarding
    pre_offer_dropoff: 'd-0300b607719e467c863f7352c3d9c11e'
    post_offer_dropoff: 'd-4e6b39b917dc41fcaaac9e1be8071397'
    upl_loan_approved: 'd-5fa5b01c07a94aaf9f22ec8545a271c6'
    upl_offer: 'd-d639466b5b0f42f5bdddc54c3db2f7a8'
    upl_onboarding: 'd-8057ae822f504266a97b15bf53390519' # used by AMS for UPL account setup
    ach_authorization_single_payment: 'd-fcb2620ce1c04408af73a46ec53a94df'
    ach_authorization_recurring_payment: 'd-96ac896ee1e84c9c86a40dbd3da8216d'
    offer_expiration_retargeting_social_proof: 'd-d1911f9714d24eff95080ebfa7426e07'
    offer_expiration_retargeting_fomo: 'd-c1b074001464444db0fc0071e13ab098'
    offer_expiration_retargeting_no_prepayment: 'd-319475aaf46a4f778e97fe95b54f9b67'
    stamped_loan_agreement: 'd-1132f1f560704e49ae038f6a3f0613c5'
    good_bye_letter: 'd-46e87bf3fb6a4b719af528f6048060eb'
    good_bye_letter_velocity: 'd-46e87bf3fb6a4b719af528f6048060eb'
    good_bye_letter_quantum: 'd-538efc2220a04192a2995d261eeac738'
