default: &default
  email_whitelisted_domains:
    - mailosaur.net
    - abovelending.com
    - beyondfinance.com

  text_message_window_central_time_from_hour:
    originations: <%= ENV.fetch('ORIGINATIONS_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR', nil)&.to_i || 10 %>
    servicing: <%= ENV.fetch('SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_FROM_HOUR', nil)&.to_i || 11 %>
  text_message_window_central_time_until_hour:
    originations: <%= ENV.fetch('ORIGINATIONS_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR', nil)&.to_i || 20 %>
    servicing: <%= ENV.fetch('SERVICING_TEXT_MESSAGE_WINDOW_CENTRAL_TIME_UNTIL_HOUR', nil)&.to_i || 18 %>
  informative_email_window_central_time_from_hour: <%= ENV.fetch('INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_FROM_HOUR', nil)&.to_i || 11 %>
  informative_email_window_central_time_until_hour: <%= ENV.fetch('INFORMATIVE_EMAIL_WINDOW_CENTRAL_TIME_UNTIL_HOUR', nil)&.to_i || 21 %>
  identity_service_auth_header: "<%= ENV.fetch('IDENTITY_SERVICE_AUTH_HEADER', nil) %>"

development:
  <<: *default

test:
  <<: *default
  email_whitelisted_domains:
    - mailosaur.net
    - abovelending.com
    - example.com

sandbox:
  <<: *default

staging:
  <<: *default

production:
  <<: *default
