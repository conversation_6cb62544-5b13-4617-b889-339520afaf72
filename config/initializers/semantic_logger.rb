# frozen_string_literal: true

require 'logstop_json_formatter'

unless Rails.env.development? || Rails.env.test?
  # Log to stdout in JSON format (container's stdout is sent to datadog for log ingestion)
  existing_stdout_appender = SemanticLogger.appenders.find do |appender|
    appender.is_a?(SemanticLogger::Appender::IO)
  end
  if existing_stdout_appender
    # sidekiq sets up its own stdout appender on startup, so we set the formatter on it
    existing_stdout_appender.formatter = LogstopJsonFormatter.new
  else
    SemanticLogger.add_appender(io: $stdout, formatter: LogstopJsonFormatter.new)
  end
end
