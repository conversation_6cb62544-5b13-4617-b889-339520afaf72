# frozen_string_literal: true

Flipper.configure do |config|
  adapter = if !Rails.env.test? && ENV.fetch('CLUSTERED_REDIS_URI', nil).present?
              if ENV.fetch('CLUSTERED_REDIS_URI').start_with? 'rediss'
                redis = Redis::Cluster.new(nodes: ENV.fetch('CLUSTERED_REDIS_URI', nil))
                Flipper::Adapters::Redis.new(
                  Redis::Namespace.new(ENV.fetch('CLUSTERED_REDIS_PREFIX', nil), redis:)
                )
              else
                redis = Redis.new(url: ENV.fetch('CLUSTERED_REDIS_URI'))
                Flipper::Adapters::Redis.new(redis)
              end
            else
              Flipper::Adapters::Memory.new
            end

  # Ref:  https://www.flippercloud.io/docs/instrumentation#advanced-setup
  #       https://github.com/wrapbook/flipper-notifications/blob/main/spec/flipper/notifications_spec.rb
  # TLDR:
  # Flipper comes with built-in instrumentation specific to Flipper Cloud.
  # By specifying ActiveSupport Instrumentation, we use the rails default
  config.default { Flipper.new(adapter, instrumenter: ActiveSupport::Notifications) }

  existing_feature_flags = Flipper.features.map(&:name)

  %w[disable_sending_sms].each do |feature|
    Flipper.disable(feature) unless existing_feature_flags.include?(feature)
  end
end

# add slackmoji
module Flipper
  module Notifications
    class FeatureEvent
      def summary_markdown
        msg = String.new("Communications-service *[#{Rails.env}]* Feature `#{feature.name}` was #{action_taken}.")

        if include_state?
          msg << ' The feature is now *fully enabled.* :large_green_circle:' if feature.on?
          msg << ' The feature is now *fully disabled.* :red_circle:' if feature.off?
        end

        msg
      end

      def action_taken
        case operation
        when 'add'
          'added :white_check_mark:'
        when 'clear'
          'cleared'
        when 'remove'
          'removed :x:'
        when 'enable', 'disable'
          'updated'
        else
          ''
        end
      end
    end
  end
end

# allow in all non-development, non-test environments
# sandbox, staging, production
if !Rails.env.development? && !Rails.env.test?
  Flipper::Notifications.configure do |config|
    config.enabled = true
    slack_webhook = Flipper::Notifications::Webhooks::Slack.new(url: ENV.fetch('FLIPPER_SLACK_HOOK', nil))

    notifier = lambda do |event:|
      slack_webhook.notify(event:)
    end

    config.notifiers = [
      notifier
    ]
  end
end
