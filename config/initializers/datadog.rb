# frozen_string_literal: true

Datadog.configure do |c|
  # Disable tracing in local dev and when running tests
  c.tracing.enabled = false if Rails.env.development? || Rails.env.test?

  # Partial flushing submits completed portions of a trace to the agent. Needed for jobs with many spans.
  # https://docs.datadoghq.com/tracing/trace_collection/automatic_instrumentation/dd_libraries/ruby/#payload-too-large
  c.tracing.partial_flush.enabled = true
end

Datadog::Tracing.before_flush(
  Datadog::Tracing::Pipeline::SpanFilter.new { |span| span.resource =~ /PingController/ }
)
