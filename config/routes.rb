# frozen_string_literal: true

Rails.application.routes.draw do
  require 'sidekiq/pro/web'
  Sidekiq::Web.use(Rack::Auth::Basic) do |username, password|
    sidekiq_username = ENV.fetch('SIDEKIQ_DASHBOARD_USERNAME')
    sidekiq_password = ENV.fetch('SIDEKIQ_DASHBOARD_PASSWORD')

    ActiveSupport::SecurityUtils.secure_compare(username, sidekiq_username) &&
      ActiveSupport::SecurityUtils.secure_compare(password, sidekiq_password)
  end
  mount Sidekiq::Web => '/sidekiq'
  mount Flipper::UI.app(Flipper.instance) => "/#{ENV.fetch('FLIPPER_UI_SECRET')}/flipper"

  mount PgHero::Engine => '/pghero'
  mount RailsPgExtras::Web::Engine => '/pg_extras'

  mount Rswag::Api::Engine => '/api-docs'
  mount Rswag::Ui::Engine => '/api-docs'
  resources :ping, only: :index
  root 'ping#index'

  namespace :api do
    resources :messages, only: %i[create index show]
    resources :sbt_webhooks, only: %i[create]
    resources :sendgrid_webhooks, only: %i[create]
  end

  namespace :utils do
    put 'email_campaigns/:campaign_name/contacts_file', to: 'email_campaigns#upload_contacts_file',
                                                        as: :upload_contacts_file
    post 'email_campaigns/:campaign_name/trigger', to: 'email_campaigns#trigger_campaign', as: :trigger_campaign
  end

  # Catch-all route to handle undefined routes
  match '*path', to: 'application#route_not_found', via: :all
end
