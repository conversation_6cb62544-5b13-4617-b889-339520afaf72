default: &default
  adapter: postgresql
  encoding: unicode
  host: <%= ENV['AURORA_HOST'] %>
  port: <%= ENV['AURORA_PORT'] %>
  username: <%= ENV['DATABASE_USERNAME'] %>
  password: <%= ENV['DATABASE_PASSWORD'] %>
  pool: <%= ENV['RAILS_MAX_THREADS'] || 5 %>

development:
  <<: *default
  database: <%= ENV.fetch('COMMS_DATABASE_NAME', 'communications_service_development') %>

test:
  <<: *default
  database: communications_service_test

sandbox:
  <<: *default
  database: communications_service_sandbox

staging:
  <<: *default
  database: communications_service_staging

production:
  <<: *default
  database: communications_service_production
  pool: <%= ENV['RAILS_MAX_THREADS'] || 10 %>
