GEM
  remote: https://gems.contribsys.com/
  specs:
    sidekiq-pro (8.0.1)
      sidekiq (>= 8.0.0, < 9)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_model_serializers (0.10.15)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    aws-eventstream (1.3.2)
    aws-partitions (1.1097.0)
    aws-sdk-core (3.223.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.100.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.185.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    axe-matchers (2.6.1)
      dumb_delegator (~> 0.8)
      virtus (~> 1.0)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    case_transform (0.2)
      activesupport
    choice (0.2.0)
    coderay (1.1.3)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.3)
    datadog (2.15.0)
      datadog-ruby_core_source (~> 3.4)
      libdatadog (~> ********.0)
      libddwaf (~> ********.2)
      logger
      msgpack
    datadog-ruby_core_source (3.4.1)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    diff-lcs (1.6.2)
    docile (1.4.1)
    dogstatsd-ruby (5.7.0)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    drb (2.2.3)
    dumb_delegator (0.8.1)
    equalizer (0.0.11)
    erb (5.0.1)
    erubi (1.13.1)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffi (1.17.2)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    flipper (1.3.5)
      concurrent-ruby (< 2)
    flipper-notifications (0.1.7)
      activesupport (>= 7, < 8.1)
      flipper (>= 0.24, < 2.0)
      httparty (~> 0.17)
    flipper-redis (1.3.4)
      flipper (~> 1.3.4)
      redis (>= 3.0, < 6)
    flipper-ui (1.3.5)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 1.3.5)
      rack (>= 1.4, < 4)
      rack-protection (>= 1.5.3, < 5.0.0)
      rack-session (>= 1.0.2, < 3.0.0)
      sanitize (< 8)
    formatador (1.1.0)
    git (4.0.1)
      activesupport (>= 5.0)
      addressable (~> 2.8)
      process_executer (~> 4.0)
      rchardet (~> 1.9)
    globalid (1.2.1)
      activesupport (>= 6.1)
    guard (2.19.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      logger (~> 1.6)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      ostruct (~> 0.6)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    hashdiff (1.1.2)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.2)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    json (2.11.3)
    json-schema (5.1.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jsonapi-renderer (0.2.2)
    jwt (3.1.2)
      base64
    language_server-protocol (********)
    libdatadog (********.0)
    libdatadog (********.0-x86_64-linux)
    libddwaf (********.2)
      ffi (~> 1.0)
    libddwaf (********.2-x86_64-darwin)
      ffi (~> 1.0)
    libddwaf (********.2-x86_64-linux)
      ffi (~> 1.0)
    lint_roller (1.1.0)
    liquid (5.8.7)
      bigdecimal
      strscan (>= 3.1.1)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    logstop (0.4.1)
      logger
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lumberjack (1.2.10)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    method_source (1.1.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    nenv (0.3.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    ostruct (0.6.1)
    pagy (6.0.4)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pghero (3.7.0)
      activerecord (>= 7.1)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    process_executer (4.0.0)
      track_open_instances (~> 0.1)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-remote (0.1.8)
      pry (~> 0.9)
      slop (~> 3.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-erd (1.7.2)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
      choice (~> 0.2.0)
      ruby-graphviz (~> 1.2)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-pg-extras (5.6.10)
      rails
      ruby-pg-extras (= 5.6.10)
    rails_semantic_logger (4.17.0)
      rack
      railties (>= 5.1)
      semantic_logger (~> 4.16)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rchardet (1.9.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redis-cluster-client (0.11.6)
      redis-client (~> 0.22)
    redis-clustering (5.4.0)
      redis (= 5.4.0)
      redis-cluster-client (>= 0.10.0)
    redis-namespace (1.11.0)
      redis (>= 4)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    rexml (3.4.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.1)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-sidekiq (5.1.0)
      rspec-core (~> 3.0)
      rspec-expectations (~> 3.0)
      rspec-mocks (~> 3.0)
      sidekiq (>= 5, < 9)
    rspec-support (3.13.4)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rswag-api (2.16.0)
      activesupport (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rswag-ui (2.16.0)
      actionpack (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rubocop (1.75.5)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    ruby-graphviz (1.2.5)
      rexml
    ruby-pg-extras (5.6.10)
      pg
      terminal-table
    ruby-progressbar (1.13.0)
    ruby_http_client (3.5.5)
    sanitize (7.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.16.8)
    securerandom (0.4.1)
    semantic_logger (4.16.0)
      concurrent-ruby (~> 1.0)
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    shellany (0.0.1)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    sidekiq (8.0.3)
      connection_pool (>= 2.5.0)
      json (>= 2.9.0)
      logger (>= 1.6.2)
      rack (>= 3.1.0)
      redis-client (>= 0.23.2)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    slop (3.6.0)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    store_model (4.2.2)
      activerecord (>= 7.0)
    stringio (3.1.7)
    strscan (3.1.5)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.3.2)
    thread_safe (0.3.6)
    timeout (0.4.3)
    track_open_instances (0.1.15)
    turbo-rails (2.0.13)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    utf8-cleaner (1.0.0)
      activesupport
    virtus (1.0.5)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
      equalizer (~> 0.0, >= 0.0.9)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  ruby
  x86_64-darwin-22
  x86_64-linux

DEPENDENCIES
  active_model_serializers
  aws-sdk-s3
  axe-matchers
  bootsnap
  brakeman
  datadog
  debug
  dogstatsd-ruby
  dotenv-rails
  factory_bot_rails
  faker
  faraday
  flipper
  flipper-notifications
  flipper-redis
  flipper-ui
  git
  guard
  guard-rspec
  importmap-rails
  jwt
  liquid
  logstop
  pagy
  paper_trail
  pg
  pghero
  pry
  pry-remote
  puma
  rails (~> 7.2.0)
  rails-controller-testing
  rails-erd
  rails-pg-extras
  rails_semantic_logger
  redis
  redis-clustering
  redis-namespace
  rspec-rails
  rspec-sidekiq
  rspec_junit_formatter
  rswag-api
  rswag-specs
  rswag-ui
  rubocop
  sendgrid-ruby
  shoulda-matchers
  sidekiq
  sidekiq-pro!
  simplecov
  sprockets-rails
  stimulus-rails
  store_model
  turbo-rails
  tzinfo-data
  utf8-cleaner
  webmock

RUBY VERSION
   ruby 3.3.7p123

BUNDLED WITH
   2.4.13
