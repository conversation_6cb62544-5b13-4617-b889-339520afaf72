#!/usr/bin/env ruby
require "fileutils"

# path to your application root and other dependent applications.
APP_ROOT = File.expand_path("..", __dir__)
AMS_ROOT = File.expand_path('../../application_management_system', __dir__)

def system!(*args)
  system(*args) || abort("\n== Command #{args} failed ==")
end

# Create Private and Public Keys in the AMS repo if they don't already exist.
# All services that perform authentication must share the same keys.
FileUtils.chdir AMS_ROOT do
  puts "\n== Application Management System =="
  if File.exist?('private.key')
    puts '== Private key exists =='
  else
    puts '== Generating JWT RSA private and public key =='
    system! 'openssl genrsa -out ./private.key 2048'
    system! 'openssl rsa -in ./private.key -pubout > ./public.key'
  end
end

FileUtils.chdir APP_ROOT do
  # This script is a way to set up or update your development environment automatically.
  # This script is idempotent, so that you can run it at any time and get an expectable outcome.
  # Add necessary setup steps to this file.

  puts "== Installing dependencies =="
  system! "gem install bundler --conservative"
  system("bundle check") || system!("bundle install")

  puts '== Copying AMS public & private keys =='
  system! 'rm -f ./public.key; cp ../alp-development/public.key ./public.key' # symlinks do not work
  system! 'rm -f ./private.key; cp ../alp-development/private.key ./private.key' # symlinks do not work

  puts "\n== Setting up local environment variables =="
  unless File.exist?(".env.development")
    FileUtils.cp ".env.example", ".env.development"
  end

  puts "\n== Preparing database =="
  system! "bin/rails db:prepare"

  puts "\n== Preparing test database =="
  system! "bin/rails db:test:prepare"

  puts "\n== Seeding database =="
  system! "bin/rails db:seed"

  puts "\n== Removing old logs and tempfiles =="
  system! "bin/rails log:clear tmp:clear"

  puts "\n== Restarting application server =="
  system! "bin/rails restart"

  # puts "\n== Configuring puma-dev =="
  # system "ln -nfs #{APP_ROOT} ~/.puma-dev/#{APP_NAME}"
  # system "curl -Is https://#{APP_NAME}.test/up | head -n 1"
end
