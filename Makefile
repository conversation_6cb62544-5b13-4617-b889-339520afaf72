.DEFAULT_GOAL := help
SERVICE ?= alp

alp.upgrade: # Upgrade to ALP v2
	@echo Copying compose files for ALP V2
	ln -s .alp/compose/compose.yml ./compose.yml
	ln -s .alp/compose/compose.development.yml ./compose.development.yml
	ln -s .alp/compose/compose.sandbox.yml ./compose.sandbox.yml
	ln -s .alp/compose/compose.staging.yml ./compose.staging.yml

alp.downgrade: # Downgrade to ALP V1
	@echo Removing ALP V2 Compose files
	rm ./compose.yml
	rm ./compose.development.yml
	rm ./compose.sandbox.yml
	rm ./compose.staging.yml
	@echo Removing alp development keys
	rm -f ./local-public.key
	@echo Removing alp environment variables
	rm .alp/.env/development
	rm .alp/.env/sandbox
	rm .alp/.env/staging

alp.secrets.pull: # Fetches Environment files from Bitwarden
	.alp/bin/pull_secrets

alp.secrets.push: # Pushes Environment files to Bitwarden
	.alp/bin/push_secrets

alp.setup: # setup ALP platform
	.alp/bin/setup

start: # Start all ALP Services
	docker compose stop
	docker compose up -d
	docker attach communications-service-alp-1

sidekiq: # Like alp.start, but attach to sidekiq instead of rails app
	docker compose stop
	docker compose up -d
	docker attach communications-service-sidekiq-1

stop: # Stop all ALP Services
	docker compose stop

exec.bash: # Bash prompt on running container
	docker compose exec $(SERVICE) bash

bash: # Bash prompt for container
	docker compose run --rm $(SERVICE) command bash

migrate: # Run database and seed migrations
	docker compose exec $(SERVICE) bin/rails db:migrate

# Show help topics
help:
	@grep -E '^[a-zA-Z0-9_.-]+:.*?# .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?# "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'
